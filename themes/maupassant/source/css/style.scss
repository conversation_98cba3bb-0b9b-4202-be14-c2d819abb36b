/**
 *
 * @package Maupassant
 * <AUTHOR>
 * @version 2.0
 * @link http://chopstack.com
 */


/*
pure css setting
When setting the primary font stack, apply it to the Pure grid units along
with `html`, `button`, `input`, `select`, and `textarea`. Pure Grids use
specific font stacks to ensure the greatest OS/browser compatibility.
*/

html,
button,
input,
select,
textarea,
.MJXc-TeX-unknown-R,
.pure-g [class *="pure-u"] {
    /* Set your content font stack here: */
    font-family: "PingFangSC-Regular", Helvetica, "Helvetica Neue", "Segoe UI", "Hiragino Sans GB", "Source Han Sans CN", "Microsoft YaHei", "STHeiti", "WenQuanYi Micro Hei", sans-serif !important;
}

body {
    background-color: #FFF;
    color: #444;
    font-family: "TIBch", "Classic Grotesque W01", "Helvetica Neue", <PERSON>l, "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "WenQuanYi Micro Hei", Sim<PERSON><PERSON>, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
}

.body_container {
    padding: 0 60px;
    max-width: 1150px;
    margin: 0 auto;
}

.content_container {
    padding-right: 50px;
    padding-top: 20px;
}

a,
button.submit {
    color: #6E7173;
    text-decoration: none;
    -webkit-transition: all .1s ease-in;
    -moz-transition: all .1s ease-in;
    -o-transition: all .1s ease-in;
    transition: all .1s ease-in;
}

a:hover,
a:active {
    color: #444;
}

a:focus {
    outline: auto;
}

.clear {
    clear: both;
}

div {
    box-sizing: border-box;
}

#header {
    padding: 58px 0 0;
    text-align: left;
    border-bottom: 1px solid #ddd;
    position: relative;

    .site-name {
        margin-bottom: 40px;

        h1 {
            padding: 0;
            margin: 0;
            height: 0;
            overflow: hidden;
        }

        #logo {
            font: bold 38px/1.12 "Times New Roman", Georgia, Times, sans-serif;
            color: #555;

            span,
            &:hover {
                color: #777;
            }
        }

        .description {
            margin: .2em 0 0;
            color: #999;
        }
    }

    #nav-menu {
        margin: 10px 0 -1px;
        padding: 0;
        position: absolute;
        right: 0;
        bottom: 0;

        a {
            display: inline-block;
            padding: 3px 20px 3px;
            line-height: 30px;
            color: #444;
            font-size: 13px;
            border: 1px solid transparent;

            &:hover {
                border-bottom-color: #444;
            }

            &.current {
                border: 1px solid #ddd;
                border-bottom-color: #fff;
            }
        }
    }
}

#sidebar {
    border-left: 1px solid #ddd;
    padding-left: 35px;
    margin-top: 40px;
    padding-bottom: 20px;
    word-wrap: break-word;

    .widget {
        margin-bottom: 30px;

        .widget-title {
            color: #6E7173;
            line-height: 2.7;
            margin-top: 0;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            display: block;
            font-weight: normal;
        }

        .comments-title {
            color: #6E7173;
            line-height: 2.7;
            margin-top: 0;
            font-size: 16px;
            border-bottom: 0px solid #ddd;
            display: block;
            font-weight: normal;
        }

        .tagcloud {
            margin-top: 10px;

            a {
                line-height: 1.5;
                padding: 5px;
            }
        }

        ul {
            list-style: none;
            padding: 0;

            li {
                margin: 5px 0;
                line-height: 1.5;

                .wl-emoji {
                    width: 16px;
                }
            }
        }

        .category-list-count {
            padding-left: 5px;
            color: #6E7173;
        }

        .category-list-count::before {
            content: "(";
        }

        .category-list-count::after {
            content: ")";
        }

        /* tiered display categories */
        .category-list-child li {
            margin-left: 0.6em !important;
            padding-left: 1em;
            position: relative;
        }

        .category-list-child li::before {
            content: "-";
            position: absolute;
            top: 0;
            left: 0;
        }

        .search-form {
            position: relative;
            overflow: hidden;

            input {
                background: #fff 8px 8px no-repeat url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6%2BR8AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAG11AABzoAAA%2FN0AAINkAABw6AAA7GgAADA%2BAAAQkOTsmeoAAAESSURBVHjajNCxS9VRGMbxz71E4OwgoXPQxVEpXCI47%2BZqGP0LCoJO7UVD3QZzb3SwcHB7F3Uw3Zpd%2FAPCcJKG7Dj4u%2FK7Pwp94HDg5Xyf5z1Pr9YKImKANTzFXxzjU2ae6qhXaxURr%2FAFl9hHDy%2FwEK8z89sYVEp5gh84wMvMvGiSJ%2FEV85jNzLMR1McqfmN5BEBmnmMJFSvtpH7jdJiZv7q7Z%2BZPfMdcF6rN%2FT%2F1m2LGBkd4HhFT3dcRMY2FpskxaLNpayciHrWAGeziD7b%2BVfkithuTk8bkGa4wgWFmbrSTZOYeBvjc%2BucQj%2FEe6xHx4Taq1nrnKaW8K6XUUsrHWuvNevdRRLzFGwzvDbXAB9cDAHvhedDruuxSAAAAAElFTkSuQmCC);
                padding: 7px 11px 7px 28px;
                line-height: 16px;
                border: 1px solid #bbb;
                width: 65%;
                -webkit-border-radius: 5px;
                -moz-border-radius: 5px;
                -ms-border-radius: 5px;
                -o-border-radius: 5px;
                border-radius: 5px;
                -webkit-box-shadow: none;
                -moz-box-shadow: none;
                box-shadow: none;
            }
        }

        .author-info {
            text-align: center;

            a.info-avatar {
                img {
                    border-radius: 50%;
                    padding: 4px;
                    width: 96px;
                    height: 96px;
                    background-color: transparent;
                    box-shadow: 0 0 10px rgba(0, 0, 0, .2);
                }
            }

            a.info-icon {
                font-size: 25px;
            }
        }
    }
}


/* title for search result or tagged posts*/

.label-title {
    margin-top: 1.1em;
    font-size: 20px;
    font-weight: normal;
    color: #888;
}

.post {
    padding: 25px 0 15px;

    .post-title {
        margin: 0;
        color: #555;
        text-align: left;
        font: bold 25px/1.1 "ff-tisa-web-pro", Cambria, "Times New Roman", Georgia, Times, sans-serif;

        a {
            color: #555;
        }

        .top-post {
            font-weight: 400;
            height: 32px;
            padding: 0 6px;
            margin-right: 5px;
            line-height: 32px;
            font-size: 16px;
            white-space: nowrap;
            vertical-align: 2px;
            color: #fff;
            background-image: -webkit-linear-gradient(0deg, #3742fa 0, #a86af9 100%);
            border-radius: 2px 6px;
        }
    }

    .post-meta {
        padding: 0;
        margin: 15px 0 0;
        color: #6E7173;
        float: left;
        display: inline;
        text-indent: .15em;

        &:before {
            font-family: "FontAwesome";
            content: "\f073";
            padding-right: 0.3em;
        }

        .category {
            &:before {
                font-family: "FontAwesome";
                content: "\f07c";
                padding-right: 0.3em;
            }
        }

        #busuanzi_value_page_pv {
            &:before {
                font-family: "FontAwesome";
                content: "\f024";
                padding-right: 0.3em;
            }
        }
    }

    .ds-thread-count {
        padding: 0;
        margin: 15px 0 0;
        color: #6E7173;
        float: right;
        display: inline;
        text-indent: .15em;

        &:before {
            font-family: "FontAwesome";
            content: "\f0e5";
            padding-right: 0.3em;
        }

        &:hover {
            color: #444;
        }
    }

    .disqus-comment-count {
        padding: 0;
        margin: 15px 0 0;
        color: #6E7173;
        float: right;
        display: inline;
        text-indent: .15em;

        &:before {
            font-family: "FontAwesome";
            content: "\f0e5";
            padding-right: 0.3em;
        }

        &:hover {
            color: #444;
        }
    }

    .post-content {
        clear: left;
        font-size: 15px;
        line-height: 1.77;
        color: #444;
        padding-top: 15px;
        text-align: justify;
        text-justify: distribute;
        word-break: break-word;

        h2 {
            margin: 1.4em 0 1.1em;
            border-bottom: 1px solid #eee;
            overflow: hidden;
        }

        h3 {
            margin: 1.4em 0 1.1em;
        }

        pre code {
            padding: 0 2em;
        }

        p {
            margin: 0 0 1.234em;
            word-break: break-word;
            overflow-wrap: break-word;

            code {
                display: inline-block;
                margin: 0 5px;
                padding: 0 5px;
                background: #f7f8f8;
                font-family: Menlo, Consolas, monospace !important;
            }

            a {
                color: #01579f;
                padding-bottom: 2px;
                word-break: normal;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .caption {
            color: #444;
            display: block;
            font-size: 0.9em;
            margin-top: 0.1em;
            position: relative;
            text-align: center;
        }

        hr {
            margin: 2.4em auto;
            border: none;
            border-top: 1px solid #eee;
            position: relative;
        }

        img {
            max-width: 100%;
            padding: 0.5em 0;
            margin: auto;
            display: block;
        }

        ul,
        ol {
            border-radius: 3px;
            margin: 1em 0;

            ul {
                margin: 0;
            }

            code {
                display: inline-block;
                margin: 0 5px;
                padding: 0px 5px;
                background: #f7f8f8;
                font-family: Menlo, Consolas, monospace !important;
            }

            a {
                color: #01579f;
                padding-bottom: 2px;
                word-break: normal;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .tagcloud {
            margin: 0 0 30px 0;

            a {
                padding: 0px 5px;
                margin: 3px;
                display: inline-block;
                border: 1px solid #808080;
                border-radius: 999em;
                color: #aaa;

                &:hover {
                    color: #fff;
                    border: 1px solid #808080;
                    background: #808080;
                }
            }
        }

        .one-tag-list {
            margin: 30px 0;

            .tag-name {
                .tag-text {
                    margin-left: 5px;
                    font-size: 16px;
                    font-weight: bold;
                }
            }
        }
    }

    .tags {
        a {
            margin-right: .5em;

            i {
                padding-right: 0.3em;
            }
        }
    }
}

.page-navigator {
    border-top: 1px solid #ddd;
    list-style: none;
    margin-top: 25px;
    padding: 25px 0 0;
    font-size: 14px;
    text-align: center;

    .page-number {
        display: inline-block;
        margin: 0 5px 5px 0;
    }

    a,
    span {
        display: inline-block;
        height: 25px;
        line-height: 25px;
        padding: 5px 9px;
        border: 1px solid #DDD;
        text-align: center;

        &:hover {
            background: #F8F8F8;
            border-bottom-color: #D26911;
        }

        &.prev {
            float: left;

            &:before {
                font-family: "FontAwesome";
                content: "\f100";
                padding-right: 0.3em;
            }
        }

        &.next {
            float: right;

            &:after {
                font-family: "FontAwesome";
                content: "\f101";
                padding-left: 0.3em;
            }
        }
    }

    .current {
        background: #F8F8F8;
        border-bottom-color: #D26911;
    }

    .space {
        border: none;
        padding: 5px 5px;
    }
}

#footer {
    padding: .8em 0 3.6em;
    margin-top: 1em;
    line-height: 2.5;
    color: #6E7173;
    text-align: center;

    span {
        font-size: .9em;
    }
}


/* for archive page starts*/

.post-archive {
    font-size: 15px;
    line-height: 2;
    padding-bottom: .8em;

    h2 {
        margin: 0;
        font: bold 25px / 1.1 "ff-tisa-web-pro", Cambria, "Times New Roman", Georgia, Times, sans-serif;
    }

    .date {
        padding-right: .7em;
    }
}


/* for archive page ends*/


/* middle*/

@media print,
screen and (max-width: 48em) {
    .body_container {
        padding: 0 30px;
    }

    .content_container {
        padding-right: 15px;
    }

    .hidden_mid_and_down {
        display: none !important;
    }

    #sidebar {
        border-left-width: 0px;
    }

    #header {
        .site-name {
            margin-bottom: 20px;
            text-align: center;
        }

        #nav-menu {
            position: relative;
            text-align: center;

            a {
                padding: 0 15px;
                line-height: 27px;
                height: 27px;
                font-size: 13px;
            }
        }
    }
}


/* small*/

@media print,
screen and (max-width: 35.5em) {
    .body_container {
        padding: 0 20px;
    }

    .content_container {
        padding-right: 0;
    }
}

blockquote,
.stressed {
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin: 2.5em 0;
    padding: 0 0 0 50px;
    color: #555;
    border-left: none;
}

blockquote::before,
.stressed-quote::before {
    content: "\201C";
    display: block;
    font-family: times;
    font-style: normal;
    font-size: 48px;
    color: #444;
    font-weight: bold;
    line-height: 30px;
    margin-left: -50px;
    position: absolute;
}

strong,
b,
em {
    font-weight: bold;
}

pre {
    margin: 2em 0;
}

.hidden1 {
    display: none;
}


/* back-to-top rocket*/

@media print,
screen and (min-width: 48em) {
    #rocket {
        position: fixed;
        right: 50px;
        bottom: 50px;
        display: block;
        visibility: hidden;
        width: 26px;
        height: 48px;
        background: url("data:image/png;base64,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") no-repeat 50% 0;
        opacity: 0;
        -webkit-transition: visibility .6s cubic-bezier(0.6, 0.04, 0.98, 0.335), opacity .6s cubic-bezier(0.6, 0.04, 0.98, 0.335), -webkit-transform .6s cubic-bezier(0.6, 0.04, 0.98, 0.335);
        -moz-transition: visibility .6s cubic-bezier(0.6, 0.04, 0.98, 0.335), opacity .6s cubic-bezier(0.6, 0.04, 0.98, 0.335), -moz-transform .6s cubic-bezier(0.6, 0.04, 0.98, 0.335);
        transition: visibility .6s cubic-bezier(0.6, 0.04, 0.98, 0.335), opacity .6s cubic-bezier(0.6, 0.04, 0.98, 0.335), transform .6s cubic-bezier(0.6, 0.04, 0.98, 0.335);

        i {
            display: block;
            margin-top: 48px;
            height: 14px;
            background: url("data:image/png;base64,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") no-repeat 50% -48px;
            opacity: .5;
            -webkit-transition: -webkit-transform .2s;
            -moz-transition: -moz-transform .2s;
            transition: transform .2s;
            -webkit-transform-origin: 50% 0;
            -moz-transform-origin: 50% 0;
            transform-origin: 50% 0;
        }
    }

    #rocket:hover {
        background-position: 50% -62px;

        i {
            background-position: 50% 100%;
            -webkit-animation: flaming .7s infinite;
            -moz-animation: flaming .7s infinite;
            animation: flaming .7s infinite;
        }
    }

    #rocket.show {
        visibility: visible;
        opacity: 1;
    }

    #rocket.launch {
        background-position: 50% -62px;
        opacity: 0;
        -webkit-transform: translateY(-500px);
        -moz-transform: translateY(-500px);
        -ms-transform: translateY(-500px);
        transform: translateY(-500px);
        pointer-events: none;

        i {
            background-position: 50% 100%;
            -webkit-transform: scale(1.4, 3.2);
            -moz-transform: scale(1.4, 3.2);
            transform: scale(1.4, 3.2);
        }
    }
}


/* *******************************************************
* Process timelime, (homepage)
******************************************************* */

#process {
    padding: 80px 0;
    background-color: #fff;
}

#process .col-md-2 i {
    font-size: 50px;
    position: relative;
    top: 10px;
}

#process .timeline-centered {
    position: relative;
    min-height: 14px;
    margin-bottom: 30px;
}

#process .timeline-centered::before,
#process .timeline-centered::after,
#process .timeline-centered::before,
#process .timeline-centered::after {
    content: " ";
    display: table;
}

#process .timeline-centered::after {
    clear: both;
}

#process .timeline-centered::before {
    content: '';
    position: absolute;
    display: block;
    width: 4px;
    background: #f5f5f6;
    /*left: 50%;*/
    top: 50px;
    bottom: 50px;
    margin-left: 10px;
}

#process .timeline-centered .timeline-entry {
    position: relative;
    /*width: 50%;
    float: right;*/
    margin-top: 5px;
    margin-left: 20px;
    margin-bottom: 10px;
    clear: both;
}

#process .timeline-centered .timeline-entry::before,
#process .timeline-centered .timeline-entry::after {
    content: " ";
    display: table;
}

#process .timeline-centered .timeline-entry::after {
    clear: both;
}

#process .timeline-centered .timeline-entry.begin {
    margin-bottom: 0;
}

#process .timeline-centered .timeline-entry.left-aligned {
    float: left;
}

#process .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner {
    margin-left: 0;
    margin-right: -18px;
}

#process .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-time {
    left: auto;
    right: -100px;
    text-align: left;
}

#process span.number {
    font-family: 'Georgia', serif, Helvetica, "Helvetica Neue", "Hiragino Sans GB", "Microsoft YaHei", "Source Han Sans CN", "WenQuanYi Micro Hei", Arial, sans-serif;
    font-style: italic;
    font-size: 20px;
    line-height: 0;
    color: #E7E7E5;
    position: relative;
    top: -4px;
}

#process .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-icon {
    float: right;
}

#process .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-label {
    margin-left: 0;
    margin-right: 70px;
}

#process .timeline-centered .timeline-entry.left-aligned .timeline-entry-inner .timeline-label::after {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: -9px;
    -moz-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.timeline-label p {
    font-family: Helvetica, "Helvetica Neue", "Hiragino Sans GB", "Microsoft YaHei", "Source Han Sans CN", "WenQuanYi Micro Hei", Arial, sans-serif;
    margin-bottom: 3px
}

#process .timeline-centered .timeline-entry .timeline-entry-inner {
    position: relative;
    margin-left: -20px;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner::before,
#process .timeline-centered .timeline-entry .timeline-entry-inner::after {
    content: " ";
    display: table;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner::after {
    clear: both;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-time {
    position: absolute;
    left: -100px;
    text-align: right;
    padding: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-time>span {
    display: block;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-time>span:first-child {
    font-size: 15px;
    font-weight: bold;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-time>span:last-child {
    font-size: 12px;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon {
    background: #fff;
    color: #737881;
    display: block;
    width: 40px;
    height: 40px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
    text-align: center;
    border: 4px solid #F5F5F6;
    line-height: 40px;
    font-size: 15px;
    float: left;
    position: absolute;
    top: 50%;
    margin-top: -20px;
    margin-left: -9px;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-icon.bg-primary {
    background-color: #303641;
    color: #fff;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-label {
    position: relative;
    background: #eee;
    padding: 30px;
    margin-left: 60px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

#process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-label::after {
    content: '';
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 9px 9px 9px 0;
    border-color: transparent #eee transparent transparent;
    left: 0;
    top: 50%;
    margin-top: -9px;
    margin-left: -9px;
}

#process .line {
    position: absolute;
    display: block;
    width: 4px;
    background: #eee;
    top: -18px;
    right: -30px;
    bottom: -8px;
}

#process .present,
#process .born {
    font-size: 14px;
    font-family: 'Georgia', serif, Helvetica, "Helvetica Neue", "Hiragino Sans GB", "Microsoft YaHei", "Source Han Sans CN", "WenQuanYi Micro Hei", Arial, sans-serif;
    font-style: italic;
    color: #333;
    padding: 10px;
    background-color: #eee;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

#process .present::after,
#process .born::after {
    left: 100%;
    top: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    border-color: rgba(136, 183, 213, 0);
    border-left-color: #eee;
    border-width: 10px;
    margin-top: -10px
}

#process .present {
    position: absolute;
    top: -18px;
    right: 0;
    margin-top: -20px;
    line-height: 100%;
}

#process .born {
    position: absolute;
    bottom: -8px;
    right: 0;
    margin-bottom: -20px;
    line-height: 100%;
}

#process .dot_tp {
    position: absolute;
    top: -18px;
    right: -35px;
    background-color: transparent;
    height: 15px;
    width: 15px;
    margin-top: -13px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    border: 3px solid #eee;
}

#process .dot_bt {
    position: absolute;
    bottom: -8px;
    right: -35px;
    background-color: transparent;
    height: 15px;
    width: 15px;
    margin-bottom: -13px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    border: 3px solid #eee;
}

@media (max-width:768px) {
    #process .line {
        right: 10px;
    }

    #process .timeline-centered .timeline-entry .timeline-entry-inner .timeline-label {
        margin-right: 30px;
        margin-left: 45px;
        padding: 20px;
    }

    #process .timeline-centered .timeline-entry {
        margin-right: 20px;
    }

    #process .dot_tp,
    #process .dot_bt {
        right: 5px;
    }

    #process .present,
    #process .born {
        right: 35px;
    }
}


/* read more*/

.readmore a {
    font-size: 14px;
    color: #444;
    margin: -10px 0;
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    float: right;

    &:after {
        font-family: "FontAwesome";
        content: "\f101";
        padding-left: 0.3em;
    }

    &:hover {
        background: #F8F8F8;
        border-bottom-color: #D26911;
    }
}


/* syntax highlight*/

figure.highlight,
.codeblock {
    background: #f7f8f8;
    margin: 10px 0;
    line-height: 1.1em;
    color: #333;
    padding-top: 15px;
    overflow: hidden;

    table {
        display: block;
        width: 100%;
    }

    // Reset for tag `pre` and  for class `.gutter`, `.code`, `.tag`
    pre,
    .gutter,
    .code,
    .tag {
        background-color: inherit;
        font-family: Menlo, Consolas, monospace;
        border: none;
        padding: 0;
        margin: 0;
        // To override cursor attribute of `.tag` components
        cursor: text;
    }

    // To align line number and line of code regardless if there is a scroll bar or not
    .gutter,
    .code {
        vertical-align: top;
    }

    // hide gutter when code is plain text
    &.plain {
        .gutter {
            display: none;
        }
    }

    // Meta bar which contains name of the file and his link
    figcaption {
        font-size: 13px;
        padding: 0 15px 20px;
        margin: 0;
        background: #f7f8f8;
        color: #999999;

        a {
            float: right;
            color: #01579f;
        }
    }

    // Gutter which contains line numbers
    .gutter {
        background: #f7f8f8;
        border-right: 1px solid #e6e6e6;
        padding: 0.3em 15px;
        user-select: none;
    }

    // Code container
    .code {
        padding: 0.3em 15px 0.3em 1em;
        width: 100%;

        pre {
            max-width: 700px;
            overflow-x: auto;
            overflow-y: hidden;
        }
    }

    // All lines in gutter and code container
    .line {
        height: 1.3em;
        font-size: 13px;
    }
}

// Gist
.gist {

    .line,
    .line-number {
        font-family: Menlo, Consolas, monospace;
        font-size: 1em;
        margin: 0 0 5px 0;
        user-select: none;
    }
}

// Highlight code coloration
.highlight {

    // General
    .comment {
        color: #969896;
    }

    .string {
        color: #183691;
    }

    .keyword {
        color: #a71d5d;
    }

    // ApacheConf
    &.apacheconf .code {

        .common,
        .nomarkup,
        .attribute,
        .variable,
        .cbracket,
        .keyword {
            color: #0086b3;
        }

        .sqbracket {
            color: #df5000;
        }

        .section,
        .tag {
            color: #63a35c;
        }
    }

    // Bash
    &.bash .code {
        .shebang {
            color: #969896;
        }

        .literal,
        .built_in {
            color: #0086b3;
        }

        .variable {
            color: #333;
        }

        .title {
            color: #795da3;
        }
    }

    // coffescript
    &.coffeescript .code {
        .title {
            color: #795da3;
        }

        .literal,
        .built_in,
        .number {
            color: #0086b3;
        }

        .reserved,
        .attribute {
            color: #1d3e81;
        }

        .subst,
        .regexp,
        .attribute {
            color: #df5000;
        }
    }

    // C/C++
    &.cpp .code,
    &.c .code {
        .preprocessor {
            color: #df5000;
        }

        .meta-keyword {
            color: #a71d5d;
        }

        .title {
            color: #795da3;
        }

        .number,
        .built_in {
            color: #0086b3;
        }
    }

    // Clojure
    &.clj .code {
        .builtin-name {
            color: #df5000;
        }

        .name {
            color: #795da3;
        }

        .number {
            color: #0086b3;
        }
    }

    // C#
    &.cs .code {

        .preprocessor,
        .preprocessor .keyword {
            color: #333;
        }

        .title {
            color: #795da3;
        }

        .number,
        .built_in {
            color: #0086b3;
        }

        .xmlDocTag,
        .doctag {
            color: #63a35c;
        }
    }

    // CSS
    &.css .code {

        .at_rule,
        .important,
        .meta {
            color: #a71d5d;
        }

        .attribute,
        .hexcolor,
        .number,
        .function {
            color: #0086b3;
        }

        .attr_selector,
        .value {
            color: #333;
        }

        .id,
        .class,
        .pseudo,
        .selector-pseudo {
            color: #795da3;
        }

        .tag,
        .selector-tag {
            color: #63a35c;
        }
    }

    // Diff
    &.diff .code {

        .chunk,
        .meta {
            color: #795da3;
            font-weight: bold;
        }

        .addition {
            color: #55a532;
            background-color: #eaffea;
        }

        .deletion {
            color: #bd2c00;
            background-color: #ffecec;
        }
    }

    // HTTP
    &.http .code {

        .attribute,
        .attr {
            color: #183691;
        }

        .literal {
            color: #0086b3;
        }

        .request {
            color: #a71d5d;
        }
    }

    // INI
    &.ini .code {

        .title,
        .section {
            color: #795da3;
        }

        .setting,
        .attr {
            color: #a71d5d;
        }

        .value,
        .keyword {
            color: #333;
        }
    }

    // JAVA
    &.java .code {
        .title {
            color: #795da3;
        }

        .javadoc {
            color: #969896;
        }

        .meta,
        .annotation,
        .javadoctag {
            color: #a71d5d;
        }

        .number {
            color: #0086b3;
        }

        .params {
            color: #1d3e81;
        }
    }

    // JavaScript
    &.js .code {

        .built_in,
        .title {
            color: #795da3;
        }

        .javadoc {
            color: #969896;
        }

        .tag,
        .javadoctag {
            color: #a71d5d;
        }

        .tag .title {
            color: #333;
        }

        .regexp {
            color: #df5000;
        }

        .literal,
        .number {
            color: #0086b3;
        }
    }

    // JSON
    &.json .code {
        .attribute {
            color: #183691;
        }

        .number,
        .literal {
            color: #0086b3;
        }
    }

    // Makefile
    &.mak .code {
        .constant {
            color: #333;
        }

        .title {
            color: #795da3;
        }

        .keyword,
        .meta-keyword {
            color: #0086b3;
        }
    }

    // Markdown
    &.md .code {

        .value,
        .link_label,
        .strong,
        .emphasis,
        .blockquote,
        .quote,
        .section {
            color: #183691;
        }

        .link_reference,
        .symbol,
        .code {
            color: #0086b3;
        }

        .link_url,
        .link {
            text-decoration: underline;
        }
    }

    // Nginx
    &.nginx .code {

        .title,
        .attribute {
            color: #a71d5d;
        }

        .built_in,
        .literal {
            color: #0086b3;
        }

        .regexp {
            color: #183691;
        }

        .variable {
            color: #333;
        }
    }

    // Objective-C
    &.objectivec .code {

        .preprocessor,
        .meta {
            color: #a71d5d;

            .title {
                color: #df5000;
            }
        }

        .meta-string {
            color: #183691;
        }

        .title {
            color: #795da3;
        }

        .literal,
        .number,
        .built_in {
            color: #0086b3;
        }
    }

    // Perl
    &.perl .code {
        .sub {
            color: #795da3;
        }

        .title {
            color: #795da3;
        }

        .regexp {
            color: #df5000;
        }
    }

    // PHP
    &.php .code {

        .phpdoc,
        .doctag {
            color: #a71d5d;
        }

        .regexp {
            color: #df5000;
        }

        .literal,
        .number {
            color: #0086b3;
        }

        .title {
            color: #795da3;
        }
    }

    // Python
    &.python .code {

        .decorator,
        .title,
        .meta {
            color: #795da3;
        }

        .number {
            color: #0086b3;
        }
    }

    // Ruby
    &.ruby .code {

        .parent,
        .title {
            color: #795da3;
        }

        .prompt,
        .constant,
        .number,
        .subst .keyword,
        .symbol {
            color: #0086b3;
        }
    }

    // SQL
    &.sql {
        .built_in {
            color: #a71d5d;
        }

        .number {
            color: #0086b3;
        }
    }

    // XML, HTML
    &.xml,
    &.html {
        .tag {
            color: #333;
        }

        .value {
            color: #183691;
        }

        .attribute,
        .attr {
            color: #795da3;
        }

        .title,
        .name {
            color: #63a35c;
        }
    }

    // Puppet
    &.puppet {
        .title {
            color: #795da3;
        }

        .function {
            color: #0086b3;
        }

        .name {
            color: #a71d5d;
        }

        .attr {
            color: #0086b3;
        }
    }

    // LESS
    &.less {

        .tag,
        .at_rule {
            color: #a71d5d;
        }

        .number,
        .hexcolor,
        .function,
        .attribute {
            color: #0086b3;
        }

        .built_in {
            color: #df5000;
        }

        .id,
        .pseudo,
        .class,
        .selector-id,
        .selector-class,
        .selector-tag {
            color: #795da3;
        }
    }

    // Lisp
    &.lisp .code {
        .name {
            color: #df5000;
        }

        .number {
            color: #0086b3;
        }
    }

    // SCSS
    &.scss {

        .tag,
        .at_rule,
        .important {
            color: #a71d5d;
        }

        .number,
        .hexcolor,
        .function,
        .attribute {
            color: #0086b3;
        }

        .variable {
            color: #333;
        }

        .built_in {
            color: #df5000;
        }

        .id,
        .pseudo,
        .class,
        .preprocessor,
        .selector-class,
        .selector-id {
            color: #795da3;
        }

        .tag,
        .selector-tag {
            color: #63a35c;
        }
    }

    // Stylus
    &.stylus {
        .at_rule {
            color: #a71d5d;
        }

        .tag,
        .selector-tag {
            color: #63a35c;
        }

        .number,
        .hexcolor,
        .attribute,
        .params {
            color: #0086b3;
        }

        .class,
        .id,
        .pseudo,
        .title,
        .selector-id,
        .selector-pseudo,
        .selector-class {
            color: #795da3;
        }
    }

    // Go
    &.go {
        .typename {
            color: #a71d5d;
        }

        .built_in,
        .constant {
            color: #0086b3;
        }
    }

    // Swift
    &.swift {
        .preprocessor {
            color: #a71d5d;
        }

        .title {
            color: #795da3;
        }

        .built_in,
        .number,
        .type {
            color: #0086b3;
        }
    }

    // YAML
    &.yml {

        .line,
        .attr {
            color: #63a35c;
        }

        .line,
        .string,
        .type,
        .literal,
        .meta {
            color: #183691;
        }

        .number {
            color: #0086b3;
        }
    }
}


/* post navigator*/

.post-nav {
    overflow: hidden;
    margin-top: 15px;
    margin-bottom: 20px;
    padding: 10px;
    white-space: nowrap;
    border-top: 1px solid #eee;

    a {
        display: inline-block;
        line-height: 25px;
        font-size: 15px;
        color: #555;
        border-bottom: none;
        float: left;

        &.pre {
            float: left;

            &:before {
                font-family: "FontAwesome";
                content: "\f0d9";
                padding-right: 0.3em;
            }
        }

        &.next {
            float: right;

            &:after {
                font-family: "FontAwesome";
                content: "\f0da";
                padding-left: 0.3em;
            }
        }

        &:hover {
            border-bottom: none;
            color: #222;
        }
    }
}


/* toc*/

.toc-article {
    border: 1px solid #bbb;
    border-radius: 7px;
    margin: 1.1em 0 0 2em;
    padding: 0.7em 0.7em 0 0.7em;
    max-width: 40%;
}

.toc-title {
    font-size: 120%;
}

#toc {
    line-height: 1em;
    float: right;

    .toc {
        padding: 0;
        margin: 0.5em;
        line-height: 1.8em;

        li {
            list-style-type: none;
        }
    }

    .toc-child {
        margin-left: 1em;
        padding-left: 0;
    }
}

@media print,
screen and (max-width: 48em) {
    .toc-article {
        margin: 0em;
        max-width: 100%;
    }

    .clear {
        padding: 2.1em 0em 0em 0em;
    }

    #toc {
        float: none;
    }
}


/* table*/

table {
    margin: auto auto 15px;
    width: 100%;
    background: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    text-align: left;

    th {
        font-weight: bold;
        padding: 5px 10px;
        border-bottom: 2px solid #909ba2;
    }

    td {
        padding: 5px 10px;
    }

    tr {
        &:nth-child(2n) {
            background: #f7f8f8;
        }
    }
}


/* article-share*/

.article-share-link {
    cursor: pointer;
    float: right;
    margin-left: 20px;

    &:before {
        font-family: "FontAwesome";
        content: "\f064";
        padding-right: 6px;
    }
}

.article-share-box {
    position: absolute;
    display: none;
    background: #fff;
    box-shadow: 1px 2px 10px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    margin-left: -145px;
    overflow: hidden;
    z-index: 1;

    &.on {
        display: block;
    }
}

.article-share-input {
    width: 100%;
    background: none;
    box-sizing: border-box;
    font: 14px "Helvetica Neue", Helvetica, Arial, sans-serif;
    padding: 0 15px;
    color: #555;
    outline: none;
    border: 1px solid #ddd;
    border-radius: 3px 3px 0 0;
    height: 36px;
    line-height: 36px;
}

.article-share-links {
    clearfix: none;
    background: #eee;
}

.article-share-element {
    width: 50px;
    height: 36px;
    display: block;
    float: left;
    position: relative;
    color: #999;
    text-shadow: 0 1px #fff;

    &:before {
        font-size: 20px;
        font-family: "FontAwesome";
        width: 20px;
        height: 20px;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: -10px;
        margin-left: -10px;
        text-align: center;
    }

    &:hover {
        color: #fff;
    }
}

.article-share-twitter {
    @extend .article-share-element;

    &:before {
        content: "\f099";
    }

    &:hover {
        background: #00aced;
    }
}

.article-share-facebook {
    @extend .article-share-element;

    &:before {
        content: "\f09a";
    }

    &:hover {
        background: #3b5998;
    }
}

.article-share-weibo {
    @extend .article-share-element;

    &:before {
        content: "\f18a";
    }

    &:hover {
        background: #d44137;
    }
}

.article-share-qrcode {
    @extend .article-share-element;

    &:before {
        content: "\f029";
    }

    &:hover,
    &:active {
        background: #38ad5a;

        &~.qrcode {
            display: block;
            text-align: center;
        }
    }
}

.qrcode {
    display: none;
}


/* search result*/

ul.search-result-list {
    padding-left: 10px;
}

a.search-result-title {
    font-weight: bold;
    font-size: 15px;
    color: #555;
}

p.search-result {
    color: #444;
    text-align: justify;
}

em.search-keyword {
    font-weight: bold;
    font-style: normal;
    color: #01579f;
}


/* Disqus Button */

.disqus_click_btn {
    line-height: 30px;
    margin: 0;
    min-width: 50px;
    padding: 0 14px;
    display: inline-block;
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0;
    overflow: hidden;
    will-change: box-shadow;
    transition: box-shadow .2s cubic-bezier(.4, 0, 1, 1), background-color .2s cubic-bezier(.4, 0, .2, 1), color .2s cubic-bezier(.4, 0, .2, 1);
    outline: 0;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    vertical-align: middle;
    border: 0;
    background: rgba(158, 158, 158, .2);
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 3px 1px -2px rgba(0, 0, 0, .2), 0 1px 5px 0 rgba(0, 0, 0, .12);
    color: #fff;
    background-color: #999999;
    text-shadow: 0
}


/* Footer Badge*/

.github-badge {
    display: inline-block;
    border-radius: 4px;
    text-shadow: none;
    font-size: 12px;
    color: #fff;
    line-height: 1.25;
    background-color: #ABBAC3;
    margin-bottom: 5px;

    a {
        color: #fff;
    }

    img {
        /*height: "calc(%s * 1.25)" % $fontsize-footnote*/
        height: 1.25em;
        vertical-align: top;
    }

    .badge-subject {
        display: inline-block;
        background-color: #555;
        padding: 4px 4px 4px 6px;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .badge-value {
        display: inline-block;
        padding: 4px 6px 4px 4px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .bg-brightgreen {
        background-color: #4c1 !important;
    }

    .bg-green {
        background-color: #97ca00 !important;
    }

    .bg-yellowgreen {
        background-color: #a4a61d !important;
    }

    .bg-orange {
        background-color: #FE7D37 !important;
    }

    .bg-yellow {
        background-color: #dfb317 !important;
    }

    .bg-blueviolet {
        background-color: #8A2BE2 !important;
    }

    .bg-pink {
        background-color: #FFC0CB !important;
    }

    .bg-red {
        background-color: #e05d44 !important;
    }

    .bg-blue {
        background-color: #007EC6 !important;
    }

    .bg-grey,
    .bg-gray {
        background-color: #555 !important;
    }

    .bg-lightgrey,
    .bg-lightgray {
        background-color: #9f9f9f !important;
    }
}


/* Waline CSS*/

#waline {
    /* 字体大小 */
    --waline-font-size: 16px;

    /* 常规颜色 */
    --waline-white: #fff;
    --waline-light-grey: #999;
    --waline-dark-grey: #666;

    /* 主题色 */
    --waline-theme-color: #34495e;
    --waline-active-color: #bababa;

    /* 布局颜色 */
    --waline-text-color: #444;
    --waline-bgcolor: #fff;
    --waline-bgcolor-light: #f8f8f8;
    --waline-bgcolor-hover: #f0f0f0;
    --waline-border-color: #ddd;
    --waline-disable-bgcolor: #f8f8f8;
    --waline-disable-color: #bbb;
    --waline-code-bgcolor: #282c34;

    /* 特殊颜色 */
    --waline-bq-color: #f0f0f0;

    /* 头像 */
    --waline-avatar-size: 3.25rem;
    --waline-mobile-avatar-size: calc(var(--waline-avatar-size) * 9 / 13);

    /* 徽章 */
    --waline-badge-color: #34495e;
    --waline-badge-font-size: 0.775em;

    /* 信息 */
    --waline-info-bgcolor: #f8f8f8;
    --waline-info-color: #999;
    --waline-info-font-size: 0.625em;

    /* 渲染选择 */
    --waline-border: 1px solid var(--waline-border-color);
    --waline-avatar-radius: 50%;
    --waline-box-shadow: none;
}


/* Darkmode CSS*/

html[data-dark='true'] {
    background-color: #121212 !important;
    filter: invert(100%) hue-rotate(180deg) brightness(105%) contrast(85%);
    -webkit-filter: invert(100%) hue-rotate(180deg) brightness(105%) contrast(85%);

    img {
        filter: hue-rotate(180deg) contrast(100%) invert(100%);
        -webkit-filter: hue-rotate(180deg) contrast(100%) invert(100%);
    }
}

.darkmode-toggle {
    background: #a2a2a2;
    width: 3rem;
    height: 3rem;
    position: fixed;
    z-index: 999;
    border-radius: 50%;
    left: 32px;
    bottom: 32px;
    right: unset;
    cursor: pointer;
    transition: all 0.5s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}


/* Links Layout*/

.link-items {
    display: flex;
    text-align: center;
    justify-content: left;
    flex-wrap: wrap;
    padding: 0px 0.5rem;
    list-style-type: disc;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 20px;

    .link-item {
        display: inline-flex;
        --primary-color: #444;

        .link-url {
            display: inline-flex;
            text-align: center;
            justify-self: center;
            line-height: 1.5;
            width: 15rem;
            color: var(--primary-color, #000);
            padding: 0.5rem 1rem;
            margin: 0.5rem;
            transition: all 0.2s ease 0s;

            .link-left {
                display: inline-block;
                line-height: 0;

                .link-avatar {
                    width: 4rem;
                    height: 4rem;
                    border-radius: 50%;
                    transition: all 0.5s ease 0s;
                }
            }

            .link-info {
                padding-left: 0.6rem;
                display: block;

                .link-blog {
                    font-size: 1.24rem;
                    font-weight: 700;
                    margin: 0.42rem 0px;
                }

                .link-desc {
                    font-size: 0.8rem;
                    font-weight: normal;
                    margin-top: 0.5rem;
                    width: 10.5rem;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }
            }
        }
    }
}


/* Recent-comments Waline Style*/

.waline-comment-content {
    margin: 6px;
    padding: 6px;
    border-radius: 6px;

    p {
        margin: 0;
        display: -webkit-box;
        overflow: hidden;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        /*这里设置第几行显示省略号，可以设置第一行或者其他行*/
    }
}

.waline-comment-content-author {
    text-align: end;
    margin-bottom: 6px;
}

/* article tags  */
.article-tag-list-item {
    font-size: 14px;
    line-height: 24px;
    display: inline-block;
    padding: 0 6px;
    border-radius: 3px;
    background: #f6f6f6;
    min-width: 30px;
    text-align: center;
    margin: 3px;
}

.article-tag-list-item a {
    text-align: center;
    color: #555;
}

.article-tag-list-item a::before {
    content: "\f02b";
    font-family: FontAwesome;
    margin-right: 4px;
}

.article-tag-list-item:hover {
    color: #555;
    background: #efefef;
}

.article-tag-list {
    padding-left: 0rem;
}