#donate {
    position: relative;
    font-family: "Helvetica Neue", Ubuntu, "WenQuanYi Micro Hei", Helvetica, "Hiragino Sans GB", "Microsoft YaHei", "Wenquanyi Micro Hei", "WenQuanYi Micro Hei Mono", "WenQuanYi Zen Hei", "WenQuanYi Zen Hei", "Apple LiGothic Medium", "SimHei", "ST Heiti", "WenQuanYi Zen Hei Sharp", Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    line-height: 1.8em;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
    height: 15em;
}

#donate img {
    border-width: 0px;
}

#donate a {
    color: #000;
    text-decoration: none;
    outline: none;
    border: none;
}

#donate .list,
.list li,
.list-left li {
    list-style: none;
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

#donate .pos-f {
    position: absolute;
}

#donate .left-100 {
    width: 100%;
    height: 100%;
}

#donate .blur {
    -webkit-filter: blur(3px);
    filter: blur(3px);
}

#donate .tr3 {
    transition: all .3s;
}

#donate #DonateText {
    position: absolute;
    font-size: 12px;
    width: 70px;
    height: 70px;
    line-height: 70px;
    color: #fff;
    background: #ffd886 url(../img/like.svg) no-repeat center 10px;
    background-size: 20px;
    border-radius: 35px;
    text-align: center;
    left: calc(50% - 120px);
    top: calc(50% - 60px);
    transform: rotatez(-15deg);
}

#donate #donateBox {
    display: table;
    left: calc(50% - 150px);
    top: calc(50% - 15px);
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    width: 299px;
    height: 28px;
    float: left;
    z-index: 1;
    overflow: hidden;
}

#donate #donateBox li {
    width: 74px;
    /*float: left;*/
    /* float: right; */
    display: table-cell;
    text-align: center;
    border-right: 1px solid #ddd;
    background: no-repeat center center;
    background-color: rgba(204, 217, 220, 0.1);
    background-size: 45px;
    cursor: pointer;
    overflow: hidden;
    height: 28px;
    -webkit-filter: grayscale(1);
    filter: grayscale(1);
    opacity: 0.5;
}

#donate #donateBox li:hover {
    background-color: rgba(204, 217, 220, 0.3);
    -webkit-filter: grayscale(0);
    filter: grayscale(0);
    opacity: 1;
}

#donate #donateBox>li:last-child {
    border-width: 0;
}

#donate #donateBox a {
    display: block;
    height: 100%;
    width: 100%;
}

#donate #donateBox #PayPal {
    background-image: url(../img/paypal.svg);
}

#donate #donateBox>#BTC {
    background-image: url(../img/bitcoin.svg);
    line-height: 28px;
}

#donate #donateBox>#BTC:hover {
    overflow: visible;
}

#donate #BTC>button {
    opacity: 0;
    cursor: pointer;
}

#donate #donateBox #AliPay {
    background-image: url(../img/alipay.svg);
}

#donate #donateBox #WeChat {
    background-image: url(../img/wechat.svg);
}

#donate #QRBox {
    top: 0;
    left: 0;
    z-index: 1;
    background-color: rgba(255, 255, 255, 0.3);
    display: none;
    perspective: 400px;
}

#donate #MainBox {
    cursor: pointer;
    position: absolute;
    text-align: center;
    width: 200px;
    height: 200px;
    left: calc(50% - 100px);
    top: calc(50% - 100px);
    background: #fff no-repeat center center;
    background-size: 190px;
    border-radius: 6px;
    box-shadow: 0px 2px 7px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: all 1s ease-in-out;
    transform-style: preserve-3d;
    transform-origin: center center;
    overflow: hidden;
}

#donate #github {
    width: 24px;
    height: 24px;
    left: calc(50% + 135px);
    top: calc(50% - 30px);
    background: no-repeat center center url(../img/github.svg);
    background-size: contain;
    opacity: 0.3;
    transform: rotatez(15deg);
}

#donate #ps {
    color: red;
    text-align: center;
    line-height: 28px !important;
    font-size: 12px !important;
}

#donate [data-footnote] {
    position: relative;
    overflow: hidden;
}

#donate [data-footnote]:hover {
    overflow: visible;
}

#donate [data-footnote]::before,
[data-footnote]::after {
    position: absolute;
    transition: all .3s;
    transform: translate3d(-50%, 0, 0);
    opacity: 0;
    left: 37px;
    z-index: 10;
}

#donate [data-footnote]::before {
    content: attr(data-footnote);
    border-radius: 6px;
    background-color: rgba(100, 100, 100, 0.8);
    color: #fff;
    height: 24px;
    line-height: 24px;
    padding: 0 6px;
    font-size: 12px;
    white-space: nowrap;
    top: -24px;
    left: 37px;
}

#donate [data-footnote]::after {
    content: '';
    border: 5px solid #333;
    border-color: rgba(100, 100, 100, 0.8) transparent transparent transparent;
    top: 0;
    left: 37px;
}

#donate [data-footnote]:hover::before,
[data-footnote]:hover::after {
    opacity: 1;
}

#donate [data-footnote]:hover::before,
[data-footnote]:hover::after {
    transform: translate3d(-50%, -7px, 0);
}

#donate #MainBox.showQR {
    opacity: 1;
    animation-name: showQR;
    animation-duration: 3s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
    -webkit-animation: showQR 3s ease-in-out 0s 1 normal forwards;
}

@keyframes showQR {
    from {
        transform: rotateX(90deg);
    }
    8% {
        opacity: 1;
        transform: rotateX(-60deg);
    }
    18% {
        opacity: 1;
        transform: rotateX(40deg);
    }
    34% {
        opacity: 1;
        transform: rotateX(-28deg);
    }
    44% {
        opacity: 1;
        transform: rotateX(18deg);
    }
    58% {
        opacity: 1;
        transform: rotateX(-12deg);
    }
    72% {
        opacity: 1;
        transform: rotateX(9deg);
    }
    88% {
        opacity: 1;
        transform: rotateX(-5deg);
    }
    96% {
        opacity: 1;
        transform: rotateX(2deg);
    }
    to {
        opacity: 1;
    }
}

#MainBox.hideQR {
    opacity: 1;
    animation-name: hideQR;
    animation-duration: 0.5s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
    -webkit-animation: hideQR 0.5s ease-in-out 0s 1 normal forwards;
}

@keyframes hideQR {
    from {}
    20%,
    50% {
        transform: scale(1.08, 1.08);
        opacity: 1;
    }
    to {
        opacity: 0;
        transform: rotateZ(40deg) scale(0.6, 0.6);
    }
}