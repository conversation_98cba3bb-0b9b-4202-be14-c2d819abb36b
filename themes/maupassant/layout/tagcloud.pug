extends base
block title
  title= page.title + ' | ' + config.title
block content
  .post
    h1.post-title= page.title
    .post-content
      .tagcloud
        for tag in site.tags.toArray()
          a(href='/tags/#' + tag.name, title=tag.name, rel= tag.length) #{tag.name}

      for tag in site.tags.toArray()
        .one-tag-list
          span.fa.fa-tag.tag-name(id=tag.name)
            span.tag-text #{tag.name}
          for post in tag.posts.toArray()
            .post-preview
              a(href=config.root + post.path, title=post.title) #{post.title}

  if page.donate
    include _widget/donate.pug
  if page.comments
    include _partial/comments.pug