mixin a_with_current(href, content, id)
  - var url = url_for(href)
  if (href == '.' && (is_home() || is_post())) || is_current(href)
    a.current(href=url)
      i(class='fa' + ' ' + id)= ' ' + content
  else if (href == 'archives/' && is_tag())
    a.current(href=url)
      i(class='fa' + ' ' + id)= ' ' + content
  else if (href == 'archives/' && is_category())
    a.current(href=url)
      i(class='fa' + ' ' + id)= ' ' + content
  else
    a(href=url)
      i(class='fa' + ' ' + id)= ' ' + content

mixin title(page)
  if page
    if page.title
      = page.title
    else
      = _p('no-title')
