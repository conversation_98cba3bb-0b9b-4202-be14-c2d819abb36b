if theme.fancybox == true
  script(type='text/javascript', src='https://unpkg.com/@fancyapps/fancybox/dist/jquery.fancybox.min.js', async)
  script(type='text/javascript', src=url_for(theme.js) + '/fancybox.js' + '?v=' + theme.version, async)
  link(rel='stylesheet', type='text/css', href='https://unpkg.com/@fancyapps/fancybox/dist/jquery.fancybox.min.css')

if theme.swiftype
  script.
    (function(w,d,t,u,n,s,e){w['SwiftypeObject']=n;w[n]=w[n]||function(){
    (w[n].q=w[n].q||[]).push(arguments);};s=d.createElement(t);
    e=d.getElementsByTagName(t)[0];s.async=1;s.src=u;e.parentNode.insertBefore(s,e);
    })(window,document,'script','//s.swiftypecdn.com/install/v2/st.js','_st');
    _st('install','#{theme.swiftype}','2.0.0');

if theme.self_search == true
  link(rel='stylesheet', type='text/css', href=url_for(theme.css)+'/search.css' + '?v=' + theme.version)
  script(type='text/javascript', src=url_for(theme.js) + '/search.js' + '?v=' + theme.version)
  script.
    var search_path = '#{config.search.path}';
    if (search_path.length == 0) {
      search_path = 'search.xml';
    }
    var path = '#{config.root}' + search_path;
    searchFunc(path, 'local-search-input', 'local-search-result');

if theme.canvas_nest.enable == true
  - var color=theme.canvas_nest.color || "0,0,0"
  - var opacity=theme.canvas_nest.opacity || "0.5"
  - var zIndex=theme.canvas_nest.zIndex || "-2"
  - var count=theme.canvas_nest.count || "50"
  script(type='text/javascript', color=color, opacity=opacity, zIndex=zIndex, count=count, src='https://unpkg.com/canvas-nest.js/dist/canvas-nest.js')

if theme.love == true
  script(type='text/javascript', src=url_for(theme.js) + '/love.js' + '?v=' + theme.version)

if theme.copycode == true
  script(type='text/javascript', src=url_for(theme.js) + '/copycode.js' + '?v=' + theme.version, successtext=__('copy_success_text'))
  link(rel='stylesheet', type='text/css', href=url_for(theme.css) + '/copycode.css' + '?v=' + theme.version)

if theme.external_css == true
  link(rel='stylesheet', type='text/css', href=url_for(theme.css) + '/external.css' + '?v=' + theme.version)

if page.mathjax
  include mathjax.pug

if page.mathjax2
  include mathjax2.pug

script(type='text/javascript', src=url_for(theme.js) + '/codeblock-resizer.js' + '?v=' + theme.version)
script(type='text/javascript', src=url_for(theme.js) + '/smartresize.js' + '?v=' + theme.version)

if theme.mermaid.enable == true
  script(type='text/javascript', id='maid-script' mermaidoptioins=theme.mermaid.options src='https://unpkg.com/mermaid@'+ theme.mermaid.version + '/dist/mermaid.min.js' + '?v=' + theme.version)
  script.
    if (window.mermaid) {
      var options = JSON.parse(document.getElementById('maid-script').getAttribute('mermaidoptioins'));
      mermaid.initialize(options);
    }