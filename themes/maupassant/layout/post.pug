extends base

block title
  title= page.title + ' | ' + config.title

block content
  .post
    h1.post-title= page.title
    .post-meta= page.date.format(config.date_format)
      if page.categories.length > 0
        span= ' | '
        span.category
          for category in page.categories.toArray()
            a(href=url_for(category.path))= category.name
      if theme.busuanzi == true
        script(src='https://busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js', async)
        span#busuanzi_container_page_pv= ' | '
          span#busuanzi_value_page_pv
          span= ' ' + __('Hits')
      if theme.wordcount == true
        include _partial/wordcount.pug
    if theme.disqus.enable == true
      a.disqus-comment-count(data-disqus-identifier=page.path, href=url_for(page.path) + '#disqus_thread')
    if theme.changyan
      a.ds-thread-count(href=url_for(page.path) + '#SOHUCS')
        span#changyan_count_unit(style='font-size: 15px; color: #6E7173;') 0
        span= ' ' + __('Comment')
      script(src='https://assets.changyan.sohu.com/upload/plugins/plugins.count.js', async)
    if theme.valine.enable == true
      a.disqus-comment-count(href=url_for(page.path) + '#vcomment')
        span.valine-comment-count(data-xid=url_for(page.path))
        span= ' ' + __('Comment')
    if theme.waline.enable == true
      a.disqus-comment-count(href=url_for(page.path) + '#waline')
        span.waline-comment-count(id=url_for(page.path))
        span= ' ' + __('Comment')
    if page.toc
      .clear
        #toc.toc-article
          .toc-title= __('contents')
          != toc(page.content, {list_number: theme.toc_number})
    .post-content
      != page.content
    if theme.donate.enable == true && page.donate != false
      include _widget/donate.pug

    if theme.post_copyright.enable == true && page.copyright != false
      include _widget/copyright.pug

    if theme.shareto == true
      script(type='text/javascript', src=url_for(theme.js) + '/share.js' + '?v=' + theme.version, async)
      a.article-share-link(data-url=page.permalink, data-id=page._id, data-qrcode=qrcode(page.permalink))= __('shareto')

    include _partial/tag.pug
    include _partial/post_nav.pug

    if page.comments
      include _partial/comments.pug
