if theme.disqus.enable == true
  .widget
    .widget-title
      i.fa.fa-comment-o= ' ' + __('recent_comments')
    script(type='text/javascript', src='//' + theme.disqus.shortname + '.disqus.com/recent_comments_widget.js?num_items=5&hide_avatars=1&avatar_size=32&excerpt_length=20&hide_mods=1')

if theme.waline.enable == true
  .widget
    .widget-title
      i.fa.fa-comment-o= ' ' + __('recent_comments')
    #widget-waline-list
    script(type='text/javascript', id="recent-comment", serverURL=theme.waline.serverURL, count=theme.waline.count ,  src=url_for(theme.js) + '/recent-comments.js' + '?v=' + theme.version, async)
