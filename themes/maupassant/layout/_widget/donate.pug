#donate
  link(rel='stylesheet', type='text/css', href=url_for(theme.css) + '/donate.css' + '?v=' + theme.version)
  script(type='text/javascript', src=url_for(theme.js) + '/donate.js' + '?v=' + theme.version, successtext=__('copy_success_text'))
  a#github.pos-f.tr3(href=theme.donate.github||'https://github.com/Kaiyuan/donate-page', target='_blank', title='Github')
  #DonateText= 'Donate'
  ul#donateBox.list.pos-f
    if theme.donate.paypal_url!=null
      li#PayPal
        a(href=theme.donate.paypal_url, target='_blank')
    if theme.donate.btc_qr!=null && theme.donate.btc_key!=null
      li#BTC(data-clipboard-text=theme.donate.btc_key, data-footnote=__('Copy_address_and_show_QRCode'), qr=theme.donate.btc_qr)
    if theme.donate.alipay_qr != null
      li#AliPay(qr=theme.donate.alipay_qr)
    if theme.donate.wechat_qr != null
      li#WeChat(qr=theme.donate.wechat_qr)
    if !(theme.donate.wechat_qr != null||theme.donate.alipay_qr != null || (theme.donate.btc_qr!=null && theme.donate.btc_key!=null)||theme.donate.paypal_url!=null)
      li#ps= __('no_donate')
  #QRBox.pos-f.left-100
    #MainBox
