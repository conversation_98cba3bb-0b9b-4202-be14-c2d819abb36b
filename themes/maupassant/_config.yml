disqus:
  enable: false ## If you want to use Disqus comment system, please set the value to true.
  shortname: ## Your disqus_shortname, e.g. username
  api: ## You can visit Disqus comments in China mainland without barriers using Disqus API, e.g. https://disqus.skk.moe/disqus/
  apikey: ## Your API key obtained in Disqus API Application, e.g. ****************************************************************
  admin: ## Username of your Disqus moderator, e.g. username
  admin_label: ## The text of Disqus moderator badge, e.g. Mod
uyan: ## Your uyan_id. e.g. 1234567
livere: ## Your livere data-uid, e.g. MTAyMC8zMDAxOC78NTgz
changyan: ## Your changyan appid, e.g. cyrALsXc8
changyan_conf: ## Your changyan conf, e.g. prod_d8a508c2825ab57eeb43e7c69bba0e8b
gitalk: ## See: https://github.com/gitalk/gitalk
  enable: false ## If you want to use Gitment comment system please set the value to true.
  owner: ## Your GitHub ID, e.g. username
  repo: ## The repository to store your comments, make sure you're the repo's owner, e.g. gitalk.github.io
  client_id: ## GitHub client ID, e.g. ********************
  client_secret: ## GitHub client secret, e.g. ec2fb9054972c891289640354993b662f4cccc50
  admin: ## Github repo owner and collaborators, only these guys can initialize github issues.
valine: ## See: https://valine.js.org
  enable: false ## If you want to use Valine comment system, please set the value to true.
  appid: ## Your LeanCloud application App ID, e.g. pRBBL2JR4N7kLEGojrF0MsSs-gzGzoHsz
  appkey: ## Your LeanCloud application App Key, e.g. tjczHpDfhjYDSYddzymYK1JJ
  serverURLs: ## If you are the "LeanCloud" in China, then this option is not mandatory to fill out. REST API Server Url" of your "LeanCloud, e.g. https://prbbl2jr.api.lncldglobal.com
  notify: false ## Mail notifier, see https://github.com/xCss/Valine/wiki/Valine-评论系统中的邮件提醒设置
  verify: false ## Validation code.
  placeholder: Just so so ## Comment box placeholders.
  avatar: "mm" ## Gravatar type, see https://github.com/xCss/Valine/wiki/avatar-setting-for-valine
  pageSize: 10 ## Number of comments per page.
  guest_info: nick,mail,link ## Attributes of reviewers.
minivaline: ## See: https://github.com/MiniValine/MiniValine
  enable: false ## If you want to use MiniValine comment system, please set the value to true.
  appId: ## Your LeanCloud application App ID, e.g. pRBBL2JR4N7kLEGojrF0MsSs-gzGzoHsz
  appKey: ## Your LeanCloud application App Key, e.g. tjczHpDfhjYDSYddzymYK1JJ
  placeholder: Write a Comment ## Comment box placeholder.
  adminEmailMd5: ## The MD5 of Admin Email to show Admin Flag.
  math: true ## Support MathJax.
  md: true ## Support Markdown.
  # MiniValine's display language depends on user's browser or system environment
  # If you want everyone visiting your site to see a uniform language, you can set a force language value
  # Available values: en  | zh-CN | (and many more)
  # More i18n info: https://github.com/MiniValine/minivaline-i18n
  lang:
waline: ## See: https://waline.js.org/
  enable: false ## If you want to use Waline comment system, please set the value to true.
  serverURL: ## Your server url, e.g. https://your-domain.vercel.app
  pageSize: 30 ## The desired number of comments shown in each page.
  wordLimit: 500 ## Limit the number of words in comments, 0 means no limit.
  requiredMeta: ['nick','mail'] ## Required user information, e.g. ['nick','mail','link']
  count: 5 ## The number comments displayed in the recent_comments widget, default is 10.
utterances: ## See: https://utteranc.es
  enable: false ## If you want to use Utterances comment system, please set the value to true.
  repo: ## The repository utterances will connect to, e.g. tufu9441/comments
  identifier: title ## The mapping between blog posts and GitHub issues.
  theme: github-light ## Choose an Utterances theme which matches your blog.
twikoo: ## See: https://twikoo.js.org
  enable: false ## If you want to use twikoo comment system, please set the value to true.
  envId: ## Tencent CloudBase envId
  region: ## Tencent CloudBase region, e.g. ap-shanghai
  path: ## Article path, e.g. window.location.pathname

google_search: true ## Use Google search, true/false.
baidu_search: false ## Use Baidu search, true/false.
swiftype: ## Your swiftype_key, e.g. m7b11ZrsT8Me7gzApciT
self_search: false ## Use a jQuery-based local search engine, true/false.
google_analytics: ## Your Google Analytics tracking id, e.g. UA-42425684-2
baidu_analytics: ## Your Baidu Analytics tracking id, e.g. 8006843039519956000
microsoft_clarity: ## Your Microsoft Clarity tracking id, e.g. zg2ctuea9j
fancybox: true ## If you want to use fancybox please set the value to true.
show_category_count: false ## If you want to show the count of categories in the sidebar widget please set the value to true.
toc_number: true ## If you want to add list number to toc please set the value to true.
shareto: false ## If you want to use the share button please set the value to true, and you must have hexo-helper-qrcode installed.
busuanzi: false ## If you want to use Busuanzi page views please set the value to true.
wordcount: false ## If you want to display the word counter and the reading time expected to spend of each post please set the value to true, and you must have hexo-wordcount installed.
widgets_on_small_screens: false ## Set to true to enable widgets on small screens.
canvas_nest:
  enable: false ## If you want to use dynamic background please set the value to true, you can also fill the following parameters to customize the dynamic effect, or just leave them blank to keep the default effect.
  color: ## RGB value of the color, e.g. "100,99,98"
  opacity: ## Transparency of lines, e.g. "0.7"
  zIndex: ## The z-index property of the background, e.g. "-1"
  count: ## Quantity of lines, e.g. "150"
donate:
  enable: false ## If you want to display the donate button after each post, please set the value to true and fill the following items on your need. You can also enable donate button in a page by adding a "donate: true" item to the front-matter.
  github: ## GitHub URL, e.g. https://github.com/Kaiyuan/donate-page
  alipay_qr: ## Path of Alipay QRcode image, e.g. /img/AliPayQR.png
  wechat_qr: ## Path of Wechat QRcode image, e.g. /img/WeChatQR.png
  btc_qr: ## Path of Bitcoin QRcode image, e.g. /img/BTCQR.png
  btc_key: ## Bitcoin key, e.g. **********************************
  paypal_url: ## Paypal URL, e.g. https://www.paypal.me/tufu9441
post_copyright:
  enable: false ## If you want to display the copyright info after each post, please set the value to true and fill the following items on your need.
  author: ## Your author name, e.g. tufu9441
  copyright_text: ## Your copyright text, e.g. The author owns the copyright, please indicate the source reproduced.
love: false ## If you want the love to appear when you click anywhere, set the value to true.
plantuml: ## Using PlantUML to generate UML diagram, must install hexo-filter-plantuml (https://github.com/miao1007/hexo-filter-plantuml).
  render: "PlantUMLServer" ## Local or PlantUMLServer.
  outputFormat: "svg" ## Common options: svg/png
copycode: true ## If you want to enable one-click copy of the code blocks, set the value to true.
dark: false ## If you want to toggle between light/dark themes, set the value to true.
totop: true ## If you want to use the rocketship button to return to the top, set the value to true.
external_css: false ## If you want to load an external CSS file, set the value to true and create a file named "external.css" in the source/css folder.
post_content_length: 180 ## Set the length of the post summary displayed on home page when no description written.
icp: ## China mainland only, show icp info on the bottom.

menu:
  - page: home
    directory: .
    icon: fa-home
  - page: archive
    directory: archives/
    icon: fa-archive
  - page: about
    directory: about/
    icon: fa-user
  - page: rss
    directory: atom.xml
    icon: fa-rss

widgets: ## Seven widgets in sidebar provided: search, info, category, tag, recent_posts, recent_comments and links.
  - search
  - info
  - category
  - tag
  - recent_posts
  - recent_comments
  - links

info:
  avatar: /img/avatar.png
  discription: To be a better man.
  outlinkitem:
    - name: twitter
      outlink: https://twitter.com/username
      message: Twitter
    - name: envelope
      outlink: mailto:<EMAIL>
      message: Email
    - name: github
      outlink: https://github.com/username
      message: Github
    - name: rss
      outlink: /atom.xml
      message: RSS

links:
  - title: site-name1
    url: http://www.example1.com/
    src: https://www.example1.com/favicon.ico
    desc: XXX's Blog
  - title: site-name2
    url: http://www.example2.com/
    src: https://www.example1.com/favicon.ico
    desc: YYY's Blog
  - title: site-name3
    url: http://www.example3.com/
    src: https://www.example3.com/favicon.ico
    desc: ZZZ's Blog

timeline:
  - num: 1
    word: 2014/06/12-Start
  - num: 2
    word: 2014/11/29-XXX
  - num: 3
    word: 2015/02/18-DDD
  - num: 4
    word: More

# Static files
js: js
css: css

# Theme version
version: 1.0.0
