# This is a basic workflow to help you get started with Actions

name: Build hexo and deploy to github pages

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [master]
  pull_request:
    branches: [master]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest
          run_install: false

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .node-version
          cache: 'pnpm'

      - name: Install dependencies
        run: |
          pnpm install --frozen-lockfile

      - name: Generate site
        run: |
          git submodule init
          git submodule update
          npx hexo generate
          cd public
          ln -sf zh-cn/* .

        # npx hexo deploy --silent

      - name: Upload artifact
        id: deployment
        uses: actions/upload-pages-artifact@v3
        with:
          name: github-pages
          path: public

  # Deployment job
  deploy_gh:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
  
  deploy_cf:
    environment:
      name: cloudflare-pages
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Download github pages artifact
        uses: actions/download-artifact@v4
        with:
          name: github-pages
      - name: Extract files
        run: |
          mkdir public
          tar -xvf artifact.tar -C public
      - name: Publish to Cloudflare Pages
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          command: |
            pages project list
            pages deploy public --project-name=${{ secrets.CF_PROJECT_NAME }} --branch=master
