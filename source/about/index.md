---
title: 关于我
---

### 简介

自 2013 年毕业后，我在上海工作了五年，专注于互联网和IT行业。我曾在外企从事 Java 开发工作一年，后来转向 Node.js 和 Golang，专注于后端开发、DevOps 和分布式系统。我积极探索 Kubernetes 和微服务架构。期间积累了丰富的经验，包括处理高并发后端架构、独立完成全栈项目、挑战技术难题，以及领导整个后端团队。值得一提的是，在此期间，我多次面临服务故障，但每次都成功解决，并事后组织团队进行详尽的复盘与整改，以确保同样的错误不再发生。

自2018年以来，我在深圳一家专注于机器视觉和智能硬件的创业公司工作，主要致力于 AI 和物联网（IoT）领域。在早期，我主要从事 C/C++ 开发工作，涵盖了多种芯片架构，包括海思、瑞芯微和星宸。通过研究这些芯片原厂的资料，我积累了丰富的经验，从资料研究到项目整理，以及项目开发流程的优化，都有所涉献。期间，我构建了多个端到端的系统，参与了 ISP 的调试，处理了硬件加密和解密，以及算法模型的转换和部署等工作。

后期，我也兼职从事产品和项目管理工作，积累了宝贵的经验。

总之，我一直没有将自己的发展方向局限在某一领域，自认为是一个终身学习者，热切欢迎各界朋友互相交流。

### 博客管理

博客内容时而精辟有料，时而划水扯淡，但主要是我的思考与总结，当然里面必然有不合理甚至错误的地方，欢迎留言。

博客用 Hexo 搭建，源码在 Github 上，欢迎
<p align="center">
  <a href="https://github.com/xizhibei/blog">
    <img src="https://img.shields.io/github/stars/xizhibei/blog.svg?style=social&label=Star">
  </a>
  <a href="https://github.com/xizhibei/blog">
    <img src="https://img.shields.io/github/watchers/xizhibei/blog.svg?style=social&label=Watch">
  </a>
</p>

### 项目

#### Golang

- [Go reverse RPC](https://github.com/xizhibei/go-reverse-rpc): 这是一个利用MQTT等持久连接协议的反向RPC框架，允许实时从云端调用设备端功能。此项目旨在简化云服务与物联网设备之间的交互过程。

#### Node.js

- [gRPC helper](https://github.com/xizhibei/grpc-helper): 这是对 grpc-node 的扩展，增加了 Promise 支持及 async/await 语法，特性包括负载均衡和基于DNS的服务发现，优化了gRPC的可扩展性，使其更适合微服务架构的集成。；
- [Blackfyre](https://github.com/xizhibei/blackfyre): 这是一个构建在 RabbitMQ 上的分布式任务队列系统，专门用于管理任务的重试、延迟及监控，从而增强分布式系统中队列管理的健壮性和可靠性。
- [Getui rest sdk](https://github.com/xizhibei/getui-rest-sdk): 这是对个推 SDK 的简化封装，提供了更易用和开发者友好的API，简化了将个推的推送通知服务集成到应用程序的过程。

### Contact

- 昵称：习之北
- 微信：xizhibei
- Email: xuzhipei at gmail dot com
- [Github](https://github.com/xizhibei)
- [Blog](https://blog.xizhibei.me)


-----

### Introduction
Since graduating in 2013, I have worked in Shanghai for five years, focusing on the internet and IT industry. I initially worked in a foreign company as a Java developer for one year, then shifted to Node.js and Golang, concentrating on backend development, DevOps, and distributed systems. I actively explored Kubernetes and microservices architecture. During this time, I accumulated extensive experience, including managing high-concurrency backend architectures, independently completing full-stack projects, tackling technical challenges, and leading backend teams. Notably, I faced several service outages but successfully resolved each one and subsequently organized thorough reviews and rectifications with my team to ensure such errors would not recur.

Since 2018, I have been working in a startup in Shenzhen focused on machine vision and smart hardware, primarily in the AI and Internet of Things (IoT) sectors. Initially, I engaged in C/C++ development, covering various chip architectures including Hisilicon, Rockchip, and StarFive. Through studying original materials from these chip manufacturers, I gained valuable experience, from research and documentation to streamlining project development processes. I built multiple end-to-end systems, participated in ISP tuning, handled hardware encryption and decryption, and managed the conversion and deployment of algorithmic models.

Later, I also took on roles in product and project management, accumulating valuable experience.

In summary, I have never confined my professional growth to a single field and consider myself a lifelong learner, eagerly welcoming exchanges with friends from all walks of life.


### Blog Management
The content of my blog varies from insightful and substantial to casual and rambling, but mainly it consists of my thoughts and summaries. Naturally, it may contain unreasonable or even erroneous parts, and I welcome comments.

The blog is built with Hexo, and its source code is available on GitHub. Feel free to
<p align="center">
  <a href="https://github.com/xizhibei/blog">
    <img src="https://img.shields.io/github/stars/xizhibei/blog.svg?style=social&label=Star">
  </a>
  <a href="https://github.com/xizhibei/blog">
    <img src="https://img.shields.io/github/watchers/xizhibei/blog.svg?style=social&label=Watch">
  </a>
</p>

### Projects

#### Golang

- [Go reverse RPC](https://github.com/xizhibei/go-reverse-rpc): A reverse RPC framework that leverages long-standing connection protocols like MQTT to enable real-time cloud-to-device function calls. This project aims to facilitate seamless interaction between cloud services and IoT devices.

#### Node.js

- [gRPC helper](https://github.com/xizhibei/grpc-helper): An enhancement of grpc-node that incorporates support for Promises and async/await syntax. Features include load balancing and DNS-based service discovery, optimizing gRPC for better scalability and easier integration in microservices architectures.
- [Blackfyre](https://github.com/xizhibei/blackfyre): A distributed task queue built on RabbitMQ, designed to handle task retries, delays, and monitoring. This project improves the robustness and reliability of queue management in distributed systems.
- [Getui rest sdk](https://github.com/xizhibei/getui-rest-sdk): A straightforward wrapper for the GeTui SDK, simplifying the integration of GeTui's push notification services into applications by providing a more accessible and developer-friendly API.

### Contact

- Nickname：xizhibei
- Email: xuzhipei at gmail dot com
- [Github](https://github.com/xizhibei)
- [Blog](https://blog.xizhibei.me)
