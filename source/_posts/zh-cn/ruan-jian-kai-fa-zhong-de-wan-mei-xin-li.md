---
title: 软件开发中的完美心理
date: 2016-05-08 23:35:28
tags: []
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/11
---
很多时候，会发现会有一种求完美的心理：
- 这个功能实现好差，重构下吧；
- 新出了个工具或者框架，在项目中试试吧；
- 试过新工具或者框架之后，恩，好棒，用来改改其他的项目；
- 哎呀，时间不够了，但是还想做更好，怎么办。。。放着吧。。。；

应该说，大部分时间，完美心理可以促进我们主动去学习新知识，新框架，尤其是最近几年，技术是日新月异，我们能及时跟进潮流。

只是，有时候，求完美的心理容易导致另一种心理：拖延心理。因为学习以及应用到实际中是需要时间的，如果发现目前没有时间的话，会把任务搁置起来，想着有时间了可以好好搞搞。哥们，别想了，你没时间的！

还有一种情况，就是一个功能过度开发，或者过度优化，从而导致项目延期，这对于开发人员来说，求完美的确是个好素质，要做就要做到最好。可是，如果是在创业初期，在做 MVP 的时候，你必须学会妥协，那时候需要的是速度，可以拖欠一些技术债务，因为需要的是生存。（呃，不差钱，不缺时间的土豪请略过……

所以，开发一个软件，你就必须得 ** 承认以及接受不完美 **，然后你才能有机会去做得更好。
#### PS

这个主题是由最近的一个感悟引起的：

每个公司，每种职业或者岗位都会有自己优缺点，没有所谓完美的工作，都会有让人委屈的地方，但是如果你只是看到不足的地方，这个点会越来越大，直到吞没所有优点。明星光鲜吧？他们苦恼而又想做的事正是你天天做的事，逛街，旅游，平淡的幸福。乞丐很难受？开玩笑，他们天天蹲着，趴着或者躺着就能天天拿钱，而且可能月收入比你工资高。

人生亦是如此，每个时间段都会有苦有甜，欢笑伴着泪水，正如老话说得好，哪会有一直顺利的人生，总会有起有伏，正是这些构成了你独一无二的人生。

首先是承认自己的人生不完美，然后接受，最后你才能去改变。受得了委屈，你才能有更大的胸怀，有人说 ** 胸怀就是委屈撑大的 **。

勇敢的骚年啊，快去创造奇迹~


***
原链接: https://github.com/xizhibei/blog/issues/11

![知识共享许可协议](https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png "署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）")

本文采用 [署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh) 进行许可。
