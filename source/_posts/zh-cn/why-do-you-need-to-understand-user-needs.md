---
title: 为什么你需要理解用户需求
date: 2020-07-14 19:24:47
tags: [工作方法,业务]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/143
---
<!-- en_title: why-do-you-need-to-understand-user-needs -->

前些天看到一篇教大家导出百万级别数据 Excel 文件的文章，我当时不由得楞了一会儿：这年头有这样的需求？不过仔细回想之前自己经历过的应用场景，发现还真有。

撇开技术细节不谈，我们以此来谈谈，作为一个技术人，为什么你需要尝试去理解用户需求。

### 从运营同学的导数据需求开始

假如你做过后端的业务，那么这样类似的数据需求是经常的，比如对于运营同学，他们经常需要你帮忙导数据。

于是你会发现，一旦你最近做完了某项需求后，紧接着而来的就是运营同学请求你导数据。于是，你需要写一个脚本：连接数据库，读出数据，写入 Excel。

经历过几次之后，你学乖了，首先是你做成了一个强大的脚本工具，调整下参数就能导出符合要求的 Excel 文件。

又过了几次，你嫌每次手工调整参数导出很麻烦，而且自己在敲代码的时候也不想被打扰，于是之后每当有新的业务需求来的时候，你会提前跟产品说好，要把数据导出的需求加到需求列表中，还给做了一个漂亮的导出页面，只要在网页上点击几次配置好参数，就能按需导出所需的文件，你几乎觉得完美地解决了运营同学的需求。

等运营同学再次要求到出数据的时候，你会扔给他管理后台的地址，然后慢悠悠走去茶水间泡一杯咖啡。

美好的时光总是短暂，终于又有一天，运营再次跑过来，说，导出失败了，你赶紧登陆服务器查看日志，发现是服务器内存太小了，导出的数据直接把内存卡爆了。

于是，为了让运营同学可以继续导出，你先加大了服务器内存，然后想着怎么解决在小内存服务器上的大数据量导出的问题。查了不少资料，**发现可以利用流的思想，一边从数据库导出，一边写文件到磁盘，这样用小内存也能导出大量的数据**。顺利上线后，运营暂时没有来找自己了。

安静的时光还是被打破了，运营同学找过来，说还是导出数据的问题，导出的 Excel 文件打不开。你首先怀疑的是对方是不分导出的文件损坏了，你也导出了一份，打开，没问题，你用怜悯的目光盯着运营同学，运营同学急了：「我的电脑上真打不开」，你耐着性子去运营同学的工位上，看着对方双击文件后，Excel 程序启动界面一直处于加载状态，于是，你的微笑逐渐消失，并凝固。打开对方电脑的系统信息，恍然大悟：「你这电脑得换了，才 2G ……」。

### 需求已经发生了改变

到如今，这个问题已经不是普通 PC 能解决的问题了，这其实就是量变带来的质变，你们业务的成长，导致了数据分析成本的上升，那个只靠运营同学用 Excel 手工分析数据的时期已经过去了，你们需要其他方案。

于是，在搜索了一番之后，你总结了几个方案：

#### 方案一：换电脑

内存 16G，CPU 也要升级，不然打开了也会卡顿。

但换电脑不会那么快，而且也不是所有的公司领导都能批准，因此只能先申请再说。

另外，就算电脑升级了，到了某个级别后，也无法用升级来解决了。

#### 方案二：分割

把一个大的 Excel 文件分割成小文件，导出的时候限制行数，定一个刚好不会卡住的数据量，超过就分割成多个小文件。

这个方案的好处在于可以用普通 PC 达到部分的数据分析需求，只要磁盘够大，就能分析不少的文件。只是这中方案会限制分析需求，如果遇到复杂的数据分析要求，比如需要的数据分散在不同的文件中，就意味着处理流程会变得很复杂，比如你需要分步骤的多次处理。

这其实类似于数据库的分片，你只能按照某一个维度去分割，而带来的副作用便是其他维度的碎片化，即当你需要其他维度的聚合分析的时候，处理成本会相应地提高很多。

如果你知道 Map Reduce，那么你就会知道，这个方案的开发成本会很高，也不是你加上运营同学在短期就能搞定的事情。

#### 方案三：换软件或者人

教运营同学学会使用 SPSS/SAS，还有各种线上的数据服务，甚至学 SQL、Python 或着 R ，然后在普通电脑也能分析导出的数据。

这个方案需要给运营培训了，一时半会儿也教不会，如果能短时间教会，那么你可以改行做培训了。

或者公司有钱的情况下，招几个数据分析师，把任务交给他们，也能解决问题，你就能专心写自己的业务去了。（当然了，另外需求也会有，你需要把数据以其它形式传给他们，这时候千万别偷懒直接把数据库的用户名密码给他们，万一他们搞出事情来，背锅的可能就是你了。）

### 回到需求本身

到这里，不知你有没有去思考运营同学的需求本身的问题，当他提出导出数据的时候，他真正需要的只是导出数据吗？

显然不是，他的需求是**根据线上产生的数据，写出可以拿给领导看的报告**。

所以，我在这里写的第一篇博客，说的就是数据导出的问题：[ELK 搭建两三事之数据需求
](https://github.com/xizhibei/blog/issues/1) ，当时的想法目前看来就是个中等数据量的导出需求。

这其实已经初具数据分析系统的雏形了，因为当时我采取的方案是把线上的数据直接导入 ES 集群，并利用 它的分析能力，再加上 Kibana 提供的 UI，这么个简单的数据分析系统，在较长的一段时间里都满足了运营同学们的需求。

后来，如果你们的业务进一步成长，带来的数据量也会变成所谓的海量，于是，你们进入到一个新世界，即所谓的「大数据」，在这个新世界里面：

1.  数据量从 M，G 变成了 T，P，E ……；
2.  涉及的机器从一台 PC 变成了一个，甚至多个数据中心；
3.  处理工具从 Excel，SPAS/SAS 变成 Hadoop、Spark、Storm ……；
4.  人员从一个开发 + 运营变成了一个 BI 部门；

一个导数据的需求，会慢慢演变成需要一个部门去解决，这其中经历可不止一次的质变，假如有机会经历这样的演变，相信对于你会是个非常拿的出手的经历。

### 最后

那，如何理解用户需求？很遗憾，我也只是个初学者，只是，你可以从思考他们背后的需求开始，比如著名的 X-Y 问题<sup>[1]</sup>。

### Ref

1.  [X-Y PROBLEM][1]

[1]: https://coolshell.cn/articles/10804.html


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/143 ，欢迎 Star 以及 Watch

{% post_link footer %}
***