---
title: 投资与择业
date: 2018-02-25 23:40:21
tags: [职场]
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/71
---
<!-- en_title: investment-and-job-selection -->

春节过完，我等外来务工人员又开始向一线城市聚集了，回到了熟悉的工位上，而接下来是三四月份，俗称金三银四，是我们蠢蠢欲动的时候，你今年可要跳槽？

<!-- more -->

跳槽对所有人来说，可不是件小事，今天的选择可能在以后被你自己认为是脑子进了水，当然，也有可能是英明决断。我们一般会说，今天你的处境，其实是由几年前的选择决定的，那么，为什么会有这个说法呢？

开始之前，先说说职业规划，或者说目标。

### 职业规划

一般来说，做开发工程师这一行的，都有个架构师，或者 CTO 的梦，那么，你可曾想过要怎么做才能达到这个目标？

有些人会说，假如我到大公司里面，那里有大平台，牛逼的同事，还有无数的机会，在那里锻炼几年之后可以空降到一个有前途的创业公司里面当 CTO，这就是光环效应的体现了，其实我也在 [谈谈人才招聘](https://github.com/xizhibei/blog/issues/63) 中也提到过，对于毕业生来说，去大公司是比较好的选择，原因亦是如此。

那么对于已经有几年工作经验的人了呢？其实我也很难说，毕竟我也是处于这个阶段的人，但重要的还是得有自己的目标。

那么，接下来，就该说说这个目标与投资的关系了。

### 职业投资

其实，不妨把你自己的**时间与精力看作资本**，各种工作的选择就是股市里面各种股票，而**你未来的收益在你决定买入的那刻就已经决定了**。

那么，要如何投资？

其实在《黑客与画家》中，Paul 就已经告诉我们了：要选择**可测量性**和**可放大性**的工作。

同时《程序员修炼之道》教我们要在一种技术刚出来的时候开始就开始投资学习，这样等技术流行的时候就有更大的机会步入行业顶尖，得到很大的回报，从而实现技术领域的低买高卖。（其实这也是**可放大性**的体现）

这两者，都是在教你如何用投资的思维去择业。

比如你可以就选择一个你认为非常有前途的创业公司，然后跟随这个公司一起成长，那么假如这个公司有一天成为一个巨头，你作为一个元老级别的人物，就能享受到这个公司成长的溢价，直接表现就是你可能拥有比较多的股份还有你在这个公司的地位，可谓是名利双收。当然了，收益与风险成正比，这两者同时也与**可放大性**成正比，毕竟真正最后能成功的，凤毛麟角。

当然，也可以选择大公司，只是要注意**可测量性**，假如你在你所在的团队里面，是一个螺丝钉的角色，即一个大项目里面，你的贡献是跟其它 99 个同事混在一起的，那么你的**可测量性**就很低，换句简单的话说：**你做的内容放到简历里去是很可能说不出什么东西来的**。假如你经历过招聘季，你应该知道『这个项目里，你担任的是什么角色，你做了什么内容？』这样类似的问题。而假如这个项目，是由你跟几个同事一起发起，切实解决了公司的问题，并进一步做大解决了行业痛点，有一定知名度（可测量性高），那么，两者孰轻孰重，无需多言。

而至于何时跳槽，可以稍稍借鉴投资的说法：假如你在股市里面讲究长期持有一只股票，大家一般会建议你在这种情况下卖出：

> **找到了更好的投资标的**：也就是说你不看好公司未来的前景了，或者你在这个公司得不到有利于你成长的东西了，而这时候有一家更好的公司向你招手，可以是 BAT 之类的大公司，也可以是一家有前途的创业公司；

### P.S.

或许之后可以加上 SWOT 分析方法，春节归来有点懒，容我先放点东西出来 🙈。

### Ref

1.  [谈谈离职和跳槽](https://www.cnblogs.com/JimmyZhang/archive/2012/11/21/2781035.html)
2.  [程序员 30 岁前，该如何规划自己的职业发展?](https://www.zhihu.com/question/67491003)


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/71 ，欢迎 Star 以及 Watch

{% post_link footer %}
***