---
title: 犯罪现场与事故处理
date: 2018-10-07 19:20:26
tags: [企业文化,工作方法,沟通]
author: xizhi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/88
---
<!-- en_title: crime-scene-and-accident-handling -->

这周插播一个有趣的主题，首先说明，这个『犯罪现场』指的是一款桌游。

<!-- more -->

### 游戏的简介

首先简单介绍下这个游戏，这款游戏我第一次跟朋友玩的时候就喜欢上了，之后便一发不可收拾，拉上了公司里的同事一起玩了起来。

由于这个游戏步骤还是挺复杂的，这里简单说明如下。

#### 牌的类别

游戏中的牌主要包括以下几种：

-   线索牌：每个人 4 张，除目击者外使用，用来指示凶手留下的线索是什么，比如有化学品、绳子、玻璃、毒药等；
-   手段牌：每个人 4 张，除目击者外使用，用来指示凶手是如何犯罪的，比如刀砍、枪击、纵火、绑架等；
-   场景牌：目击者使用，每一轮展示 6 张；
-   身份牌：用来抽取
-   效果牌：每一轮闭眼阶段

#### 角色

-   侦探：找出凶手；
-   目击者：帮助侦探找出凶手，类似于杀人游戏里面的上帝；
-   凶手：避免被找出来；
-   帮凶：帮助凶手逃脱；

#### 阶段

1.  准备阶段：给大家发牌，每个人抽取自己的身份牌，顺便观察其它人的牌；
2.  杀人阶段：凶手与帮凶睁眼，凶手向目击者指出自己的一张手段牌与线索牌；
3.  指证阶段：目击者会抽取场景牌，四张蓝色场景牌，一张黄色场景牌指示地点，一张红色场景牌（固定不变），根据凶手的选择而将 1-6 个标示物放置于效果牌之上表示场景的重要程度：1 表示最重要，6 表示最不重要。
4.  发言阶段 1：大家睁眼，根据场景牌进行发言，然后确认是否指认凶手，这个指认机会的机会在整局游戏中只有一次机会，而且需要指认出确切的手段牌与线索牌；
5.  推进阶段 1：假如凶手没有被指认正确，则继续进入『第二个晚上』，目击者让凶手以及帮凶睁眼，询问帮凶是否要帮凶手换一张线索牌，假如是的话，目击者让帮凶选择一张线索牌，然后根据是否替换抽取两张场景牌，否则就是只抽取一张线索牌，最后替换已有的场景牌，并重新放置标示物；
6.  发言阶段 2：同发言阶段 1；
7.  推进阶段 2：推进阶段 1；
8.  发言阶段 3：同发言阶段 1，但是需要强制破案了，也就是假如之前没有破案的侦探，需要在这一回必须指认凶手；

整个游戏过程，是目击者帮助侦探们破案的过程，因此，目击者不用担心标示物摆的不好，因为可以先按直觉摆好，然后根据发言阶段的侦探们的发言重新调整表示物。

目击者需要注意的便是努力区分出凶手与侦探们的，比如凶手与另外一个侦探各有化学品以及毒药线索牌，那么毒杀相关的场景的重要程度便最好不要放高，可以放一个相对重要程度低的位置。

凶手需要注意的是，需要尽量与其它侦探们的牌联系起来，不然如果你的特征比较明显的话，便很容易被指认出来，记得我们玩的一局游戏中，当大家睁眼后，都把目光直接集中在了一个人的牌上，逼得他『自首』了，因为他的牌太容易与其它侦探们区分出来了。

### 事故处理

上面说了一大堆，跟事故处理有什么关系呢？

有，而且关系很紧密：可以按照这个游戏的过程来帮我们理顺事故处理流程。

1.  主持人将事故相关的人全部集中起来；
2.  每个人梳理自己的工作，回顾事故前后做过的工作内容，也就是将所有的线索列出来；
3.  依次发言，说出自己的推理；
4.  主持人依据合理性依次分配任务给相关人，去验证以及恢复服务；

好了，其实是我瞎扯的，真正的事故处理流程也不会这么干，但是这个游戏可以帮助团队**提高协作能力**倒是真的，因为这就是桌游的一个重要目的。

另外，这个过程也能帮助大家提高**推理能力**，这种能力在处理事故的时候特别重要，因为需要需要根据现有的线索尽快找出问题以及解决方案。

还有就是这个游戏玩得好，还能提升自己的魅力值，或者说领导力，因为假如你经常能在破案的过程中，起到关键作用，那么其它侦探对你的信赖程度就会提高，进而成为关注的焦点，同样你在真实问题处理的过程中，大家也会倾向于以你的意见为优先。同理，作为队友，你也能在这个游戏中，发现其它队友的能力。

最后，还有个点也很重要，当大家玩过多次游戏，大家都互相挺了解了，也会比较倾向于在处理问题的时候**包容队友，就事论事，尽快解决问题，而不是苛责引发事故的人，毕竟都不确定之后自己会不会成为『凶手』。**

### 建议

1.  团建的时候多玩玩桌游；
2.  处理问题的时候，15 分钟内都解决不了的，相关人全部集中到会议室，同步信息，然后讨论问题以及解决方案；

### Ref

1.  <https://baike.baidu.com/item/%E7%8A%AF%E7%BD%AA%E7%8E%B0%E5%9C%BA/8463747>


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/88 ，欢迎 Star 以及 Watch

{% post_link footer %}
***