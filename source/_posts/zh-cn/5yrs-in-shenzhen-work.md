---
title: 我来深圳这五年之工作篇
tags: [总结,创业,职场,深圳]
date: 2023-11-09 13:34:31
---

这个时间点，挺尴尬的，因为我又一次经历创业失败了，[嘟嘟](https://github.com/xizhibei/blog/issues/59)的坟头草也没长多高。不过，我倒是有时间又可以写一些回忆了，这次就不仅仅写创业历程了，也顺便写点工作之外的事情，不过全放在一篇里面会让工作之外的内容有些尴尬，因此我把重篇幅的工作篇章放这里，其它的另外写一篇。

<!-- more -->

我们先从工作说起，其实[上次换工作的时候](https://github.com/xizhibei/blog/issues/92)，我也说了一些内容，可以作为参考。

### 正文
先简单介绍下我所在的公司，叫算子(确实，我也觉着看起来挺高深的)，这个名字的由来，是因为创业初期就是以 AI 方向为主，深度学习离不开各种各样的算子，所以这个名字跟 AI 还是关联很紧密的。

我是 2018 年末加入的，加入的原因[上次](https://github.com/xizhibei/blog/issues/92)也说过了，不再赘述。距今刚好 5 年有余，因此在深圳的 5 年基本上也可以说是在算子的 5 年。

这 5 年，我把历程分为三个阶段：

#### 第一阶段：默默探索
我们在这个阶段属于拿了融资不知道干什么的阶段，打算凭借人脸算法的优势去做一些项目。

我刚加入那会儿，处于刚好完成了煤矿识别相关的项目，但是接下来的方向还是找了挺久，打算尝试用人脸算法去做商场人流量识别，并以此给商场跟店铺做一些会员的管理内容。这个涉及到做硬件，同时也是我们第一次开始做硬件相关的项目，在深圳找了商场进行测试，效果挺炫酷的，但客户不愿买单，他们认为这个系统的价值不是很高。（后来发现有其它公司的把类似的项目做起来了，看来还是我们自身的原因。）

后来我们凭借在之前积累的人脉，开始尝试做了校园课堂学生状态管理这个项目，也是差不多的套路，还是做硬件项目，内容简单来说根据课堂上学生的人脸识别情况进行统计管理，不过由于忽视客户真实需求以及疫情开始导致项目失败。

这两个涉及到硬件的项目，我第一次听到了海思以及瑞芯微，也是第一次接触到 SoC 。

再后来，就是疫情开始了。我们开始做人脸 SDK 封装，我们学着商汤旷视，做了趣视视觉这个产品，卖 License 为主，那时候给我们这个产品设计了一个自认为很完美的 License 激活方案，也是第一次在嵌入式系统里面做东西，还是非常新奇的。前期效果还是不错的，我们甚至还运营了一个微信群，卖出去了一些，不过后来被疫情打乱了节奏，这个方向也没有坚持下去，因为我们发现了更有前景的东西。

#### 第二阶段：绝境逢生
尝试过我们 SDK 的客户提了需求，要求我们把现有人脸算法封装进一款 Android 人脸门禁设备，我们完成之后，觉得这是个不错的落地应用，于是，我们也开始做人脸门禁产品，但是做的是 C++ 版本的，因为觉得 Android 版本的硬件成本高，C++ 的版本虽然开发成本高，只要出货量大就可以把成本摊薄。

期间我觉得 C++ 开发网络部分太慢，因此在试验了 Golang 版本之后，觉得开发效率提升不少，于是干脆就把网络部分全用 Golang 来实现了，但由于我们是个小团队，我不仅需要做 C++ 嵌入式的部分，也全揽下了用 Golang 实现的网络部分，另外服务器的活也压在我身上，毕竟创业公司需要人成为多面手。

做了半年左右有了成品，比预想快了三月有余，不过这次也同样不会那么轻松，没有收入叠加疫情影响，中间经历了快要倒闭的情况，所有人降薪，而老板借钱给我们发工资（不得不感慨，没有赌性，难做老板）。不过，在做出了 Demo 之后也遇到了一个不错的客户，我们凭借自己的研发能力给对方留下了不错的印象，因此也做成了第一笔大订单，这就算是绝境逢生了。

有了前面攒下来的经验基础，以及第一桶金，接下来，便是结合当时的防疫要求，我们开始做防疫产品。其实一开始做的是给人脸门禁加上了测温功能，后来老板找到了资源，才给我们做的人脸门禁加上了健康码核验功能，可以用来核验国家健康码（即国康码）是否是真实的，这是个可谓非常有价值的功能，市面上同类产品还比较少，因为那时候出现了有人凭借假的健康码混过了门岗检查这样类似的事件，于是我们对外宣传做了健康码智能核验终端。

虽然没有一炮走红，但是逐渐的，我们的客户开始变多了，并且也有客户开始陆续下单。

我们真正开始大卖，是随着疫情防控政策的收紧，以及各种疫情事件层出不穷之后。政府部门也意识到，健康码是个非常不错的工具，于是各地政府也开始大力推广自己健康码，并且健康码也确实在各地的防疫过程中，扮演了非常重要的角色。我们提供的产品，非常符合市场的需求，为防疫核验健康码提供了非常便利的方式，毕竟也不需要门岗来肉眼查看颜色了，同时也能够在很大程度上，防止健康码作假。

我们的优势还是挺明显的，因为大力投入资源在软件方面，尤其是在健康码这样的偏软件场景中。另外就是，对接资源这块，我们的效率非常高，能够及时解决客户的问题与需求。**这也是小公司比较核心的竞争力，尤其是在对于一些中小客户来说，我们这样的小公司，能够积极配合，并且效率很高地去帮他们解决问题，这是大公司很多时候无法做到的。**

我们的劣势也很明显，这个过程中，我们出现的最大问题，莫过于**硬件质量**了。我们都是互联网以及软件出身，对做硬件的了解不深，质量把控的问题非常大。这个问题在订单多的时候不是很明显，随着后期订单下降了，有客户会因为质量问题给我们退货，我们的返修率一度非常高。

供应链也是个大问题，它不仅关系到硬件质量，还有产能的问题，我们跟不上，就会丢订单。我们经客户介绍，开始跟行业的同行合作，我们提供软件，移植到他们提供硬件上，这就是个互利互惠的方式，我们凭借他们的硬件能力能够更快占领市场。事实也证明了这一点，在后来的统计中，几乎一半的设备都是经他们卖出去的。（不过后来他们借此抓住了机会，稳住了原本的客户之外，也抓住了一些中大型客户，并且在我们的软件先发优势逐渐失去的情况下，凭借硬件优势抢占了我们的市场，我除了暗自叹息之外，只剩佩服了。）

我处于中心地带，可谓收获颇多。

在这个过程中，我们没有专职的产品经理，因此很多需求都是我直接经手，我根据客户的需求，设计了几个有意思并且实用的功能：

1. **扫码配置与升级**：这是个非常有趣且实用的功能，背景是我们的设备没有加触屏，需要客户进一个网络的 Web 后台配置，导致客户配置机器非常麻烦，而且终端部署的地方不一定有人会操作，客户有时候需要开车几百公里去操作。我思考过好多解决方案，突然某天看着扫码器发呆了好久，灵感就来了（可能这就是所谓的「念念不忘，必有回想」），二维码不就是个信息输入载体么？**既然可以把健康码作为二维码信息，当然也可以把配置信息作为二维码信息**，其中当然还可以包含升级链接，于是花了两天做出了 Demo ，大家试用了之后，一致觉得非常有用，于是推给客户用，没想到过了两周，他们几乎完全放弃了进入后台配置的方式，毕竟扫个码那么简单的事情方便太多了。这样做了之后，可以让我们针对不同的客户需求，制作不同的升级二维码与配置即可，能够让我们非常快速地迭代软件，对接各地平台的时候最快可以在半个小时的时间内给到客户，并且升级部署成功。

2. **设备监控**：这个背景是我觉着如果把设备卖出去之后，如果无法及时收到反馈的话，我们会非常被动，于是我想到既然服务器可以监控，我们卖出去的设备也可以监控，我在我们服务器监控平台中，加入基于 MQTT 协议的设备监控实现，能够让 Prometheus 收集设备中的运行指标，接下来只要配置监控图标以及告警即可。这套简单的方案可以让我们了解所有设备的各种运行状态，能够比客户更快发现问题，有时候客户找上门的那刻，我们就已经他找我们的原因了。同时，我们也能非常快速的统计所有设备的各种运行状态，给我们的日常运营提供了非常大的指引作用。

3. **远程管理**：这个功能与设备监控是配合使用的，可以让我们能够远程快速定位问题，查看实时日志，调整系统参数，有些问题甚至可以远程直接处理掉，这个也简单，基于 MQTT 做个远程穿透就行。

前期对接主要靠我支撑，那时候工作强度非常高，很多时候都是当天客户提交，第二天甚至当晚就要结果，一句「明天领导要看」，就能压倒所有人。不过这个过程也挺充实的，不光是因为我知道越早部署好，想过防疫工作就能越快开展，也因为我们也能拿到订单。其中有次非常深刻，那天晚上已经非常累了，但还是硬撑着帮客户把对接搞定了。事后老板告诉我，那次搞定，客户直接下单了几百台机器，这就是成就感的来源了。

不过，揽下太多活也不好，那段时间身心非常疲惫，效率下降不可避免（给诸位交个底，博客断更也是那时候开始的），最遗憾的一件事情也发生在这个时候，我没控制住自己的脾气，怼了一个客户，间接导致他们选了其它同行，丢了挺大的订单。痛定思痛，我们继续扩张，招新人，我也慢慢把任务分派给其他新来的同事，并且教他们一步步入手，到最后完全脱手，开始做其它一些更重要的事情，尤其是产品方向问题。一段时间下来，我慢慢体会到领导者的意义，相对于上一份工作的领导岗位，我觉得自己开始真正入门管理。

这里也顺便说说几件管理上的收获：

1. **三级等保**：在主导这件事之前，听说过二级等保，不过等看完三级等保的资料后，还是头大了好几圈，幸好那时候我们的服务器安全措施还是有一些基础的，并没有花多少精力就搞定了，不过后来发现他们连服务以及设备上的应用安全也要审核的时候，还是头疼了挺久，所幸最终通过了。这个过程，是我第一次体会到「治大国若烹小鲜」，也就是指定菜单「计划」，调动公司所有的资源把所有的食材「各种材料、人力、物力」准备好，控制好火候「事情推进的节奏」，及时洒调料「控制关键节点」，然后等所有材料审核通过「煮熟后一锅出」。

2. **敏捷纠偏**：这是我整个管理过程中，最有收获的一段时间，因为那时候明明采取的是敏捷开发流程，但是服务开发团队效率非常低下。我静下心来研究了挺久，才意识到我们采取的一直是小瀑布开发模式，也就是在一个短周期的迭代中，进行所有需求的统一「规划-开发-测试-上线」流程，一旦有任务阻塞，就意味着当前迭代所有需求都无法上线，而正常的迭代是所有需求应该是独立的，且必须要控制并行的需求数量。定位到问题就好办了，给大家进行敏捷的培训，好好回顾了下一个该有的迭代是怎么样的，讨论并实施了几项措施，陪着大家进行一个完整的迭代过程，没想到效果非常显著，进行到第二次迭代的时候，需求上线速度快了非常多，几乎每天都能看到成果。

3. **管理监督**：职业生涯中遇到了第一个品德较差的同事，他利用我们的信任，在给他负责的服务器上部署自己的服务赚钱，这勉强忍了。但是他离职之前就开始准备盗用客户资源，最后几周的代码带走不提交，逼着我只能反编译，发现他还给特么给应用挂了马。等我发现后，死不承认，给我气了好久，如今回想起来，这也算是给我好好上了一堂管理监督课了。

吹了一波自己，说回我们公司的业务，我们算是转型成功了，累计卖出了不小的数字，团队快速扩张，并且以此为基础陆续做了防疫扫码盒子以及手持设备，一度成为健康码核验防疫产品中的行业龙头。期间我也过了把做 Android 客户端的瘾，因为中间了解过 Flutter ，想着怎么用到我们的产品中，这不，机会就来了，用了一个星期，Demo 就出来了，我做了几轮迭代之后，就交给了后来新招的专职 Android 研发同事。

后来我们对我们的生意模式做了一些总结，其实我们就是以做 ToG/ToB 项目的方式卖防疫硬件产品，靠着对定制需求的「不挑食」，对接了大大小小几百个健康码平台。当然了，这些平台的背后都是各地大数据局在支持，即使我们的业务直接是 ToB，但是本质上还是 ToG。

我认为产品的成功，很大程度上归结于我们建立的**快速迭代机制**，即在基于标准产品功能的基础上，快速为客户开发定制的需求，然后给客户部署完毕，获取反馈，进行调整后尽快满足客户需求，同时在这个过程中，积极收集终端客户的反馈以及运行数据，一旦我们发现了这个功能可能是普遍性的需求的话，我们会将这个功能合并进主分支，并推广给其他客户。

#### 第三阶段：坠落悬崖
成也萧何，败也萧何。我们最初成功的原因，也是后来失败的原因，随着**疫情政策**的放开，我们的状况可谓情转直下，需求端的客户几乎消失不见。

其实，我们预想过这件事情，甚至考虑过提前转型，只不过没想到它是以这样的方式来临，并且来的那么快。我们仓促应对，业务收缩，开始第一次裁员。接下来，我们调整部署，考虑到我们已经积累的销售渠道，开始回归人脸门禁本身，做考勤方向的人脸门禁。

在做考勤人脸门禁之后，我们凭借之前的经验教训，将之前的产品大刀阔斧做了不少修改，同时更注重产品质量，因此产品出来之后，质量比防疫产品提升非常多。不过，我们也意识到了之前防疫产品的质量是多么的不足，基本功不够扎实，等待防疫需求退潮，发现我们才是那个没穿内裤的，如今回想起来，那些客户对于产品质量的抱怨不绝于耳。

现实还是狠狠打了我们的脸，我们高估了市场的温度，经济下行的程度让我们始料未及，那疫情刚放开的那几周，也是我遇到的人生中最冷的时光，平时热闹的大街上几乎都没吃饭的地方，只能用荒凉来形容。而今年 2023 年疫情放开之后的开春，让我们感受到市场无以伦比的寒冷，于是我们又经历了几次裁员，**直到最后，我也不得不离开**。有时候，我会回想，如果半年之前，我们能够快速止损，清退所有业务，积极寻找新方向的话，现在可能没有那么狼狈。只是，**既然是创业，不轻易放弃是基本素养，如果我们那么容易放弃的话，在疫情初期我们就已经解散了**。至于我自己，还是获得了不少经验，不仅因为了解了完全陌生的硬件行业，还因为最后产品跟项目最终都是我来做了，毕竟初期的产品跟项目要么离职，要么被裁，除了职位的上升能够让人思维升级，换工作内容也不例外，正所谓「屁股决定脑袋」。

有句话说的好，「凭运气赚的钱，总会凭实力亏掉」，虽然说的是股市，但是用来形容我们的创业历程也有一些对的地方。我们凭借着疫情防控的浪潮活了下来，确实有运气成分存在，但是没有实力的话，其实也抓不住运气，因为运气是给有准备的人的，所以我们常说「运气也是实力的一部分」。最终难以活下来，的确是我们自己的实力有问题，但面对这个寒冷的市场，也有比我们实力更强的公司更早倒下了。

### 后记
我一直认为自己运气挺好的，在互联网行业快要进入下行期的前两年，我开始进入智能硬件行业。

其实过程非常痛苦，毕竟不是那么容易转行的，跳出自己舒适区也非常难，我大多数时间还是喜欢做自己擅长的软件相关的工作，管理是一知半解，也不会有人来手把手教我怎么做，连我最陌生的硬件设计，我也得知道是怎么回事，最后还得把自己当成项目经理跟产品经理。当团队里面的所有人都不会或者不想做某件事情时，我需要跳出来去承担，比如 ISP 调试、RTSP 实现，或者人脸算法移植（现在回想，真有种我不入地狱，谁入地狱的感觉）。

可能这就是创业的魅力，也是我从老板身上学到的最重要的素质：我们要一直去挑战不可能，当其它所有人做不到，或者说不可能的时候，我就要跳出来说：「我不信，让我来试试」。毕竟如果事情都那么简单，那我们为什么还要去创业，简单的事情大家都做，凭什么给你机会？乖乖去其它公司打工，按照别人给你的既定路线走不就得了，**创业就是要去做不可能的事情**。另外，我认为即使去其它公司打工，也会面临类似的情况，毕竟简单的事情好做，但是只有难的事情才能体现你的价值。

如果你问遗憾吗？那肯定有，而且不少，一方面遗憾我在工作中没有做到更好，另一方面遗憾我没有早做准备，这两点本身是矛盾的，因为我一直是处于坚持的状态，无法也没有去想如果公司倒了怎么办。

而如果你问后悔吗？不后悔，人生没有后悔药，更不会所有事情都一帆风顺，以后的路还很长，收拾收拾，可以准备下一段旅程了。
