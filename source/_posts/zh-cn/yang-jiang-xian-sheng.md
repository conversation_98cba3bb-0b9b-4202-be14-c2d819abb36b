---
title: 杨绛先生
date: 2016-05-25 22:04:15
tags: [读书]
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/16
---
今天在朋友圈刷屏了，不想跟风，干脆在这里写点东西，权当念想。

第一次听说杨绛先生，是在高中，有篇语文中学到的，关于她跟钱钟书先生的故事，记不清了，那时候很奇怪，为啥女的也可以叫先生，后来才知晓，先生是一种很少的女士能有的尊称。

她的那句 **『你的问题就在于读书太少而想的太多』**，足够受用终生，很多人，尤其是我自己，在几年短短的职业生涯中，感受到，很多问题，不能干想。

刚工作的时候，记得天天刷知乎，觉得上面的知识好多，于是如饥似渴的天天看，每看到一篇好文章便觉得，『哇，好棒，今天又学到了一个小知识』。
直到有一天，猛然发现，自己知道的小知识很多，但是都是乱七八糟的，** 不成体系 **，以为自己看了几篇分析经济的文章便懂经济了，以为看了摄影技巧自己就会拍照了。。。

工作中，碰到很多技术问题，想到的都是直接去查 Google，不是说不好，而是，往往有这么个习惯：查了之后，比如 stack overflow 上面有人贴出了代码，于是直接复制粘贴。现在，我觉得，好习惯在于你遇到了某个问题，查到了解决方案，明白了问题原因，探究了原理，尝试不同的解决方案，并总结，如果发现自己的解决方案更好，那么不妨开启自己的开源之路，去实现这个，甚至这类问题的通用性解决方案。

另外，多看书，技术书没得说，多看更要多实践，然后其他的书也得多看看，拓展下自己的思路，你终会发现，很多问题在各个领域都是相同的，往深远了说的话，都是哲学。（插播一条有意思的小知识，维基百科上，你去点击大部分词条内容里面的链接，来个深度搜索，你会发现 90% 以上都会最终到了哲学相关的词条）

别多想，多看书，好了，我要去看书了。


***
原链接: https://github.com/xizhibei/blog/issues/16

![知识共享许可协议](https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png "署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）")

本文采用 [署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh) 进行许可。
