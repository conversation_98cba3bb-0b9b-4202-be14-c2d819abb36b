---
title: 从数据处理脚本到人际沟通
date: 2017-07-08 15:39:54
tags: [工作方法,沟通]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/52
---
众所周知，在工作中，我们会有很多的时间与同事去沟通，而沟通效率是一支团队凝聚力的体现以及战斗力的保障。

那么，我们从一个故事说起。

### 故事

工程师小王刚毕业就进入了一家大公司里面，目前已经开始接受任务，某一天，直属项目领导 A 过来找到小王：

『小王，这里有个处理数据的任务，需要你写个脚本，处理完等下我发给你的所有的数据。』

『好的，我尽快完成。』

然后闷头去干活了。

过了两天，领导 A 过来问小王
『小王，任务完成了吗？』

『还没有呢。』

『还有多久。』

『快了。』

『哦，好。』

又过了两天
『小王，完成了？』

『本来预计昨天能完成的，有个 bug 一直修复不了，我觉得是设计有问题，那一块的代码我打算重新实现。』

『那个，小王，现在我必须批判你了，这个任务已经做了 4 天了，现在没时间了，你做不好的话就把任务给老刘。』

小王急得满脸通红：『领导，请再给我两天，我保证完成任务。』

『这样，这个类似的任务之前老刘做过，你去问问他。我现在要去开个会，这个任务不能再拖了，你需要在今天下班前给我做好。』

小王看着领导绷着脸匆匆地走了，自己很沮丧，也很无奈：『我都那么认真努力的去做这个任务了，不就是迟了两天么，至于么。』

在这时，不远处看到了整个过程的老刘，慢悠悠的走过来，笑眯眯地说到：

『小王，被领导批评的感觉如何？很爽吧。』

『哎，老刘，你就别逗我了，郁闷着呢！刚好领导也让我找你聊聊，你说说，我有啥问题？』

『我知道你刚来公司，对领导吩咐的任务很上心，而且这几天我看到你加班加点地干活，很不容易，只是呢，你做事的方法不对。你看，领导一个任务吩咐下来，你当时问清楚需求了吗？连什么时候截止都没问吧？』

『对。。。』

『既然你知道怎么去做，为什么不把预计时间跟领导说清楚？』

『我做好了直接说不就行了么？』

『那你做好了吗？』

『没。。。』

『你看，你做的这个程序，是用来处理数据的，假如这个程序运行了几个小时才能得到结果，然后过程中不告诉你进度，你会怎么做？』

『杀掉进程，看看是不是程序有问题』

『那假如你是领导，而这个程序是你的手下呢？』

『哦！我应该给领导做个进度条！』

『进度条？呵呵，也算，那再想想，假如这个程序崩溃了，中途退出，而你又去做其它事情了，你会希望这个程序会有什么功能？』

『我会想要它能打印错误栈，以及退出前保存进度，等我把问题解决了之后能够接着跑。』

『不错，那假如你是领导呢？』

『我会希望手下的人如果出了他解决不了的问题了，能够及时反馈，说明原因，然后我想办法帮他处理，不耽误任务进度。』

『很棒，总结下。』

『恩，**开始做的时候，第一点需要搞清楚需求，然后跟领导说清楚需要多少时间完成，然后第二点就是及时汇报进度，第三点就是有任何耽误进度或者我无法处理的情况，及时汇报，请求帮助。**』

『行了，挺有悟性，其实还有第四点，在任务结束的时候。。。』

『让我自己想想』
老刘露出一脸欣慰的样子。

『我知道了，就如程序本身，我希望这种需要运行长时间的程序结束的时候，告诉我它运行了多久，耗费了多少资源，出过什么问题等等，好让我能够改善这个程序。而对于我来说，**我需要给领导一个任务汇报，告诉他这个任务耗时，这个过程中发生过什么问题，有什么解决方案。**』

『很棒，来，让我看看你的程序什么问题。』

（本故事纯属虚构）

### 结论

其实在计算机工程师所在的各个行业中，我们会有非常多的时间与机器去『沟通』，这时候，我们通常会忘记与身边的人去沟通，而对于一个团队，甚至一个公司来说，任务总是由具体的人去做的，而这个人往往不是一个人，因此沟通显得格外重要。

也可以说，沟通能力是一种软技能，一个优秀工程师的必备素质，甚至可以说是决定性的素质，想象一个任务，由于沟通的问题，而导致最后到发布日期前重新实现的『事故』吧。因此，在一个任务中，你需要留出大部分时间用来沟通，别以为开会总是浪费时间，开会也是一种沟通方式，关键是看会议的效果。

我也有不会沟通的时候，就如故事中的小王，很容易沉浸在自己的这个小世界中去钻研各种高大上的技术，而轻视了了与周围同事以及上级的沟通，导致了工作中很多的问题。这就是 **『只见树木，不见树林』** 的短视表现吧。

现在回想过来，真是有些羞愧。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/52 ，欢迎 Star 以及 Watch

{% post_link footer %}
***