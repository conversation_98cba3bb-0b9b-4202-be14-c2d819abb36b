---
title: 关于公共服务的思考
date: 2016-12-07 21:12:53
tags: []
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/32
---
前几日与一同行交流，一一交流下来，发现什么叫『固步自封』，跟外界交流少了，很多东西便会搞不清楚，甚至脱离主流。

比如最近一直在为团队做基础设施方面的工作，但是，会有一种吃力不讨好的感觉，虽然搭建完毕之后自己会很很有成就感，但是随之而来的维护成本却是很让人头疼。

是的，**『能花钱的，就不要花时间』**。

我也想反驳，但后来仔细回想，没有立即反驳是因为我认同这句话。我当时想反驳的便是：公共服务就像公交车，的确有时候会很方便，可是一旦你想自由些便是很困难，这时候便需要私家车。没错，私家车成本高，还要花时间金钱维护，但是，它就是比公交车方便。

所以，对于一个创业公司来说，你完全可以用公交服务，比如代码托管，文档管理，项目管理，云服务器，监控服务，CI&CD 服务。实际上，现在公共服务越来越多，创业成本实际上越来越低，最后可能到一种程度了之后，只要使用的人搭积木即可了，所有的服务都可以是公共的、现成的。

只是，我觉得高成本的服务才是有做成公共服务的硬需求的，也是价值非常高的，比如云服务主机，安全，APM，大数据等。

好了，话说回来，我列出有些部分为什么不用公共服务：
两个词：** 成本与收益 **

#### 成本：
- 金钱：应该算是机器了，无论云服务或者自己买机器
- 时间：搭建与维护
- 人力：需要专门的人去维护
- 安全：数据不会泄露

#### 收益
- 时间：反馈时间，形成一个高效的正负反馈系统
- 可用性：满足需求，甚至比公共服务好用

这里插一句前提，国内的很多公共服务并不怎么好用，而且让人怀疑，虽然他们一再声明不会去查看甚至泄露用户数据。而国外的服务有非常多好用的，但是由于网络问题，得投入 VPN 成本，另外，他们很多是使用美元结算的，换算成 RMB 之后，很贵，不过他们的成本本来就很高。(T_T 国外好幸福。。。

比如当初选择 gitlab，一个是因为 github 有时候太慢，线上部署代码的时候太慢，换成 coding 之后，又觉得不如 github 好用。于是自己用 gitlab，然后那时候，gitlab 已经比较新，自带了 pipeline，可以直接作为 CI&CD。

然后是日志系统，目前国内好用的服务比较少，国外有个 loggy 不错，只是按照我们的需求来的话，至少得要 $5000 + 了，还不如自己搭建了，另外，由于需要上传数据到国外服务器，VPN 的成本不会太低，更别说有延时了。(其实还有点小私心，想要借此机会熟悉 ELK

好了，每个人都会有自己的选择，但是从个人角度来说：** 生命不息，折腾不止 **。

### PS
目前就我接触下来，用过的产品中，ping++ 挺棒，起码会让人觉得好用，文档非常棒，虽然现在的公司因为担心数据没用。再吐槽下个推，文档真让人头大。。。

### PPS
国内的服务目前还处于发展阶段，我觉得做出好产品是需要高成本的，并且跟创业公司是互相成就的，有条件的情况下还是多支持国内的同胞吧。。。

### Reference
1. https://github.com/qinghuaiorg/free-for-dev-zh
2. https://github.com/ripienaar/free-for-dev



***
首发于 Github issues: https://github.com/xizhibei/blog/issues/32 ，欢迎 Star 以及 Watch

{% post_link footer %}
***