---
title: 谈谈服务稳定性
date: 2018-05-06 20:15:34
tags: [企业文化,监控]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/76
---
<!-- en_title: lets-talk-about-the-service-stability -->

### 前言

服务的稳定性，对于任何一个在线提供给用户服务的公司来说，都是非常重要的，更遑论当这个服务是与充值消费相关的时候。

于是，任何一次线上事故，都可能会给公司带来显而易见的损失：因为相比于其他部门，负责服务端的小伙伴们，发生事故的时候很容易暴露在风口浪尖之中，一次代码 Bug 或者数据库调整等变更，都可能带来直接的损失，并且这种损失是快而直接的，出问题那段时间里，所有的目光都会集中在你们身上，而之后的间接损失就更不用多说了。

而像市场、运营或者产品部门，他们做的东西需要上线之后才有反馈，而这反馈时间比较慢，不会像事故那么快而明显，并且他们往往会被认为是直接给公司带来收入的部门（是的，研发会被认为是一个成本中心）。但实际上，他们那些部门带来的损失反而更大：因为**浪费**或者**方向性错误**，尤其是产品，他们做的内容可能需要经历过几个迭代周期后才会被发现是否是个好功能。而这个过程中，需求的管理也是个问题，[因为假如需求管理没有做好，会导致大量进行中的任务，造成浪费](https://github.com/xizhibei/blog/issues/44)。

而假如你作为一个技术部门的领导，你与 CEO 以及高管的沟通至关重要了，需要让他们明白你们能处理好线上事故，一旦被认为是不稳定中心，那就很危险了。

### 回到问题

显然，即使别的部门有问题，也不能说我们就不用做服务稳定性的工作了。

而且，回到问题本身，这种特殊的位置也有好处，因为相当于你们是暴露在所有人尤其是 CEO 以及高管眼底下：既是最容易出问题的部门，同时相对来说也是最容易做出成绩的部门。

### 开始分析问题之前

稳定性是个非常复杂的课题，因为这涉及到『零错误容忍』，也就是说，只要有一环出了问题，那么就会导致问题，但也不是完全没法解决。在继续之前，有三点需要说明：

首先，你需要承认，稳定性问题是一种成本，那稳定性本身就是收益，因此，解决问题需要考虑成本与收益；

其次，稳定性涉及的方面很多，问题背后是人员，人员背后是流程制度，流程制度背后是企业文化，换句话说，每一个问题都是水面上的冰山，背后往往能牵扯出非常多的问题，而水面下的问题才是真正需要解决的问题，这深层次的原因就往往最终会回到企业文化上面；

最后，**线上服务的稳定性体现了一个公司对技术的尊重程度**；

### 问题的解决思路

可以先听听这个关于扁鹊的故事

> 扁鹊是古代的神医，经他之手，几乎没有解决不了的大病，大家都认为他是当时医术最好的医生，但是有一天，魏文王求教他，他的几个兄弟之间，谁的医术最好，他说自己大哥是最好的，魏文王不明白，明明世人都认为扁鹊的医术是最好的，扁鹊就解释道：大哥是会在病人没觉得自己得病之前就会把人治好，所以世人不认可大哥的医术，而他的二哥是在疾病有一点点征兆之后就把病给治好的，所以世人认为二哥只能治小病，而他自己在治的病的时候，都是病人最危急的时候，然后世人会看到他用各种神奇的手段让病人恢复，因而觉得他医术最好。

这个故事对我今天讲的题目很有启发作用：**把问题从开始阶段解决掉，防患于未然，那就是最牛的技术**。然而现实中，如果管理层对技术不了解，就很容易造成把解决问题的『扁鹊』当成优秀员工，而真正把问题的解决在发生之前的『大哥和二哥』却无法被赏识。

所以，也就引出了这个问题的解决思路：**创造一种尊重技术的文化，鼓励大家从源头预防问题，同时在对待问题上，不是简单地处罚了事，而是真正鼓励更好地做事情，鼓励大家互相合作，坐下来一起总结分析问题，勇于曝光真正的原因，最后更好预防问题。**

> 这里插一句，其实我更向往的文化是创新，正如之前在[从把 SLA 加进 KPI 考核说起](https://github.com/xizhibei/blog/issues/53)提到的 Netflix 企业文化：我们所处的互联网行业本身就是创新的行业，把时间线拉长，你会发现没有创新的企业往往会被创新企业所淘汰，在这个前提下，你会发现鼓励创新文化更重要，预防错误仍需要做，但不要妨碍创新，能够相对容忍错误，因此更快更好处理问题在长远来看反而是最重要的。

以上，我说了解决问题的三个步骤：

#### 预防问题

这个步骤显然是最难的，但也是最容易入手的，线上问题的大部分来源有这么几个部分：

1.  人为疏忽：线上擅自调试代码、擅自动线上数据库；
2.  基础设施：数据库爆了、机房光缆被挖断了；
3.  代码复杂度：欠了太多的技术债；

针对第一点，良好的制度与流程能避免大部分，而针对基础设施以及代码复杂度，就是成本大户了，需要提前设计好架构以及及时还上技术债。

另外就是需要重视监控报警，因为监控在这个过程中，可以帮助提醒问题、发现问题以及定位问题。

#### 处理问题

这里有个原则：故障发生时恢复大于原因查找，也就是说需要通过各种手段，把事故的影响面减到最少，并且尽快恢复服务。

一般来说，这些手段主要有[1]：

> -   重启和限流：重启很容易理解，限流需要用到相关的流控中间件；
> -   回滚操作：回滚操作一般来说是解决新代码的 bug，把代码回滚到之前的版本是快速的方式；
> -   降级操作：并不是所有的代码变更都是能够被回滚的，如果无法回滚，就需要降级功能了。也就是说，需要挂一个停止服务的故障公告，主要是不要把事态扩大；
> -   紧急更新：也就是热修复 bug；

在这个过程中，强大的 CI&CD 系统太重要，可以看看我之前写的[持续交付的实践与思考](https://github.com/xizhibei/blog/issues/42)以及[CI 系统搭建](https://github.com/xizhibei/blog/issues/26)。

#### 总结问题

可以先看看大厂是怎么做的，比如阿里与亚马逊，他们非常重视故障复盘，需要提交故障报告，这个报告里面的内容主要有这么几个部分[1]：

> -   故障处理的整个过程
> -   故障原因分析
> -   问五个为什么
> -   故障后续整改计划

其中特别需要注意的是，**问五个为什么**，这个步骤最初来源于丰田公司怎么处理事故的[2]，通过连续问五个以及以上的问题，基本就能定位到问题的根本原因。

> 我的汽车无法启动。（问题）
> 为什么？：电池电量耗尽。（第一个为什么）
> 为什么？：交流发电机不能正常工作。（第二个为什么）
> 为什么？：交流发电机皮带断裂。（第三个为什么）
> 为什么？：交流发电机皮带远远超出了其使用寿命，从未更换过。（第四个为什么）
> 为什么？：我一直没有按照厂家推荐的保养计划对汽车进行过保养和维护。（第五个为什么，根本原因）[2]

另外，还有季度回顾会议，这个会议上，主要做的事情是：

1.  回顾上个季度中的所有事故；
2.  统计所有事故故障后续整改计划执行情况；
3.  总结易犯错误，给出解决方案，并完善规范跟流程；

通过以上这三个步骤，形成正向循环，逐渐改善线上服务的稳定性。

### P.S.

结尾说个小故事

> IBM 公司在 1914 年几乎破产，1921 年又险遭厄运，20 世纪 90 年代初再次遭遇低谷。但是，在一次次纠错中，他们最终都战胜了暂时的困难。有一次，IBM 公司的一位高级负责人由于工作严重失误，造成约 1000 万美元的损失，他为此异常紧张，以为要被开除或至少受到重大处分。后来，董事长把他叫去，通知他调任，而且还有所提升。他惊讶地问董事长为什么没把他开除，得到的回答却是：“要是我开除你，那又何必在你身上花 1000 万美元的学费？”[3]

选择公司的时候，文化很重要。

### Ref

1.  [『极客时间 - 左耳听风』陈皓](https://time.geekbang.org/column/intro/48)
2.  [五个为什么](https://zh.wikipedia.org/wiki/%E4%BA%94%E4%B8%AA%E4%B8%BA%E4%BB%80%E4%B9%88)
3.  《卓越管理的 88 个黄金定律》


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/76 ，欢迎 Star 以及 Watch

{% post_link footer %}
***