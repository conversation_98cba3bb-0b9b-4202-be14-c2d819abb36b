---
title: 不要让你的队友失败
date: 2018-09-24 00:22:48
tags: [工作方法,总结,沟通,职场]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/87
---
<!-- en_title: donot-let-your-teammates-fail -->

第一次看见这句话，来自于『二爷鉴书』，也就是之前 [工程师与产品经理相处之道](https://github.com/xizhibei/blog/issues/60) 提到的邱岳的公众号。

这句话还是让我挺受启发的，因为遇到过相似的场景。

### 与客户端同事

记得有次在与客户端同时讨论技术方案的时候，我当场发脾气了。

当时与客户端讨论的是如何实现我们产品中的某项多客户端数据同步的进一步需求。这种数据的同步在以前的实现很简单，我们是按数据项的对比方案来实现的，即：

1.  客户端请求服务端，拉取列表，对比本地存在的列表，主要对比 ID 以及更新时间戳；
2.  然后将本地不存在的数据添加进本地存储，而将远程不存在的发送给服务器；
3.  最后根据时间戳，将早于服务端时间的数据项重新拉取详细数据；

而现在的需求，变成了需要增加列表的分组，并且分组信息也要同步，于是情况就开始变复杂了，如果只是在当前的基础上继续添加同步的对比项，那么情况就会变得难以处理，并且在多设备同步时也会很容易出现数据不一致情况。

因此，我提出了一种改动比较大的方案，即基于操作日志的同步方案（具体的情况不多说，如果有兴趣另写一篇，这里只是说说主题相关的）。而就在我说出这个方案后，听到却是：『这要增加多少请求啊』、『开发难了点，不好做』、『要改动很大，不行』。

这让我听了后就很受不了，情绪当场失控：『不要说做不了，这种方案能解决当前的问题，不要跟我说做不了，要不你们提自己的方案！』

这句话把客户端同事也激怒了，之后的情况可想而知，虽然我之后克制了情绪，开始问他们为什么做不了、能说说具体做不了的原因之类的试图拉回讨论，但最终还是让这次需求会议不欢而散。

会后我想了想，我生气的点在于：我当时认为客户端同时只是由于想偷懒，难度有点高而不考虑这个方案，显得不靠谱。

所以，这次会议，是我让同事『失败了』。

后来还是做了些补救，包括给客户端同事发去了完整的实现文档，找客户端领导沟通，打听客户端同事最近的任务安排是不是有点紧张等等，让我欣慰的是，客户端同事没有计较我的情绪失控。

这次的结果，让我想到，我不能让客户端同事失败，那么我就需要让他们对这个方案心里有底，信任我，那么我就应该做好提前沟通，而不是在会议上临时提出这个解决方案，另外在他们提出质疑的时候，也不要马上认为他们就是偷懒或者不想做事；而最后即使会议上没有解决，我也可以另外约时间，而这个缓冲的时间内，我可以找他们了解真正的想法。

### 与运维同事

长期以来，我在团队内部长期以来都是在推广 DevOps 的，有一段时间里我们与运维同事的关系一般，毕竟很多情况下，应用部署，维护以及事故处理都用不到跟他们过多沟通，但是，打交道总避免不了的。

比如一旦我们出了线上事故，影响的都是双方，这时候就会需要通力配合，配合得好，事故就处理得快。另外就是我在推广新工具的时候，就会觉得他们水平不行，不会告诉他们细节，让他们参与的只是机器的系统维护，但是其它方面都是我们自己在维护，这在我看来，是一种效率非常高的方式。

只是，事实告诉我，我让他们『失败了』，因为我只顾着自己团队内部推广工作的维护与使用方式，而没有给运维同事提供相同的培训，而导致两方会有一些信息差，于是我们不知道他们在干什么，他们也不知道我们在干什么，我不让他们接触到应用级别的维护，他们也不给我系统级别的维护权限。

这种情况造成了我们合作层面的问题，甚至直接影响到了事故的恢复时间。

后来，意识到了这一点后，我就开始主动让运维同事参与到我们线上运维的过程中去，教他们使用 Git 管理线上的配置，以及自动化部署，开放团队文档与他们共享：让他们知道我们干了什么，内部技术分享的时候，也会拉上他们一起；同时他们也会积极学习新工具，新知识，并开始积极配合维护：我们也能得到他们的快速响应，合作起来变得很顺利。

### 最后

这句话很值得琢磨，因为这让我提醒自己，身边的同事都是一个战壕里的战友，而不是互相对立的敌人，我们需要共同解决问题为团队创造价值，而不是制造隔阂与不信任。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/87 ，欢迎 Star 以及 Watch

{% post_link footer %}
***