---
title: 2016 年总结
date: 2017-01-01 00:41:50
tags: [总结]
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/34
---
在这个跨年的时刻，躺在床上，听着音乐，想着快过去的 2016，以及即将到来的 2017。

### 2016 回顾
年初经历了加入了一年半的创业公司倒闭，开始到处找工作。然后，发现自己似乎落后这个世界好远（因为差点把自己按 15k 给买了 ==#），毕竟当自己专注于创业这件事的时候，对外界似乎关注太少。

不过，正是这创业期间不断逼自己成长，让我能很快适应新工作。

在新公司呆了一年不到，时间过得太快。总的来说，自己还是觉得成长有限，很多时候都是靠自己摸着石头过河，也有段时间，自己都静不下心来好好学习。

坚持了大半年的博客，这是我有些意外的，原本只是想随便写点东西，之前也写过，但是一直没坚持，现在在 github，专门开了个 blog 的 repo，开始写博客。也许你会问为什么在这里写，为啥不搞个静态生成器，github 的 page 还是不错的，的确，对外展示漂亮的博客吸引人眼球，但是：** 我懒 **，我只是想安安静静写点东西，记录下自己的想法，总结下经验，这里 ** 简单，干净，方便 **，对我就足够了。

技术上，这一年走过不少弯路，但也攒了不少经验，学习的东西也更多，更深入了。

我想，这一年来，最大的收获就是『** 决断 **』能力，几次处理线上事故的经验，让我能更快也更自信的处理线上事故，敢于做出大胆的决策。

### 2017 年计划

总体来说：『打好基础』，自己的基础知识，以及团队的基础。

1. DevOps：努力将 SLA 做到至少三个 9；
1. 管理自己：希望自己能更好安排与管理自己的时间；
1. 读书：读更多书，很多时候，只是自己想的太多，看的太少；
1. 写博客：沉淀知识，也多交朋友，互相学习与成长；
1. 旅行：多走走，外面的世界很大，祖国大好河川去的太少；
1. 多交流：沉浸在自己的小世界里，总会闭塞；



***
首发于 Github issues: https://github.com/xizhibei/blog/issues/34 ，欢迎 Star 以及 Watch

{% post_link footer %}
***