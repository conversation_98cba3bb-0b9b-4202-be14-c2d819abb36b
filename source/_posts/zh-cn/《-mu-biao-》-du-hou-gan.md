---
title: 《目标》读后感
date: 2017-03-29 23:57:42
tags: [读书]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/44
---
最近刚看完了《目标》，是《凤凰系统》里面提到的一本书，同样是小说题材，有趣而容易理解。

书的核心点在于 **『 TOC 制约法』** （ TOC 即 Theory of Constraints ），简单来说，就是需要发现与改进系统中的 **『瓶颈点』**。 

### 故事简介
作为工厂厂长的小说主人公 Rogo ，通过大学老师 Jonah 的指导，突破常规，大胆革新，运用 TOC 方法让工厂摆脱险境，扭亏为盈。

其实对于现代的互联网公司，这本书任然非常具有借鉴意义：因为方法论都是相通的。比如刚开始遇到的痛点，Rogo 的工厂，一开始就是生产秩序混乱，生产成本居高不下，以及交货周期非常长。

对比当下互联网的公司，其实这个问题一直都有，尤其是创业公司：产品开发流程混乱，随意更改、插入新需求，开发周期非常长，开发成本也非常高。CEO 整天头疼不已：为啥我的这帮属下，整天这么忙，每个都这么狼性，整天加班，为啥产品开发出来总是那么差呢？

下面简单总结下小说里面提到的一些内容：

#### 目标
在小说中，Jonah 与 Rogo 的第一次见面，就说出了 Rogo 目前面临的问题，于是告诉他找出『目标』，就与我们的常识一致：找出目标，我们才能知道接下来做事的方向，一旦方向偏差，我们也能快速找回来。

但是，在遇到问题的时候，能找到目标却很难：需要头脑非常冷静，你不能仅仅忙于解决目前遇到的紧急的事情，因此你需要能停下来分析与思考，才能找出目标是什么，以及接下来的做事方向。

一般来说，大部分的我们以及公司的目标只有一个：**『赚钱』**。

附上摘抄的两句话

第一句：
> 我想要告诉你的是，除非你知道目标是什么，否则生产力就毫无意义可言。 -- Jonah

第二句：
> Rogo ，如果你和世界上其他人几乎没什么两样，你毫不质疑的就接受了很多事情，表示你没有真正的思考。 -- Jonah

#### 三个指标
找到目标之后，如何衡量当前的方向与目标的差异呢？

**『指标』**

- 有效产出：整个系统通过销售而获得收入的速度；
- 存货：整个系统投资在采购上的金钱；
- 营运费用：整个系统为了把存货转为有效产出而花的钱；

对应到互联网公司就是：

- 有效产出：新产品、新功能给公司带来的收入；
- 存货：开发到一半，或者完成之后没有交付到用户手中的产品或新功能；
- 营运费用：公司对人员、机器、市场、研发等等的投入；

对应到 IT 服务系统就是：

- 有效产出：吞吐量提高，响应时间减少；
- 存货：响应还没有到达前端 UI 的显示上；
- 营运费用：服务器、电力、运维等投入；

其实，这三个指标就能帮我们做紧靠目标的决策，而不是根据以往的经验或者成见来做决策。

#### 五个步骤

- 确定唯一目标
- 找出系统瓶颈
- 决定如何挖掘制约因素的潜能
- 确定每件事都要配合比瓶颈的节奏
- 给瓶颈松绑

这些其实就是具体的问题解决流程，贯穿整个小说中故事的主线，也给了我们解决现实中遇到问题时候的解决思路。

比如当我们遇到服务器瓶颈的时候：

- 我们唯一的目标是『用户能在我们提供的服务上，消费更多』，我们就要提供更好更稳定的服务；
- 服务定位就像找 bug，好好用好监控系统；
- 一旦定位到瓶颈服务之后，我们的策略有：给瓶颈服务提供单独的服务器、升级服务器、将功能拆分，只用它来处理最关键的输入等等；
- 分配人员资源跟时间，制定策略；
- 实施策略，从而给瓶颈服务松绑；

（其实我们有成熟的策略：即在立方体的 xyz 上面拓展）。

#### 转变思维
『整个系统的产出，取决于瓶颈的产出』，这一点对于我来说是醍醐灌顶的：当我以为，瓶颈损失了一个小时的产出之后，仅仅是局限于瓶颈本身的时候，事实却恰恰相反，因为瓶颈损失的一个小时内，系统没有任何有效产出，所以也相当于整个系统损失了一个小时，或者说：『系统真正每个小时的成本是整个系统的总营运费用除以瓶颈的有效工作时间』。

#### 学会沟通
当你发现问题的时候，或者觉得这本书提到的方法可以应用到你的公司的时候，你会如何做？是自己一个人默默推进么？

显然不是，主人公获得老师指导之后，首先做的是跟属下的人沟通讲解，试图让他们一起帮忙理解，同时也为之后的改革赢得了他们的支持。

#### 与精益思想的区别
从解决问题的视角来说，我认为精益思想是『贪心』，它认为局部最优就是全局最优，而 TOC 是动态规划，因为它真正从全局来解决问题。

或者说，两者的格局也不一致：通常的管理者会认为只要公司里每个员工都用 100% 的付出，甚至通过加班加点来达到更多的产能，就能使公司又更多的盈利，但格局更高的管理者会从系统整体去思考，每个员工的都很忙碌的公司往往不是高效率的公司，因为无谓的忙碌加班只会浪费资源：** 有效产出无法增加，增加的只是无用的存货 **。

问题需要从源头上解决：俗话说的『上面得病，下面吃药』，说的就是这种没有从源头上解决问题的做法。公司是一个完整的系统，如果不能以系统的视角来看待整个公司，我们只能在局部挣扎，就如同物理学上的力学原理：所有的力量发挥到最大，就能使物体的移动速度最快吗？别忘了，作用在物体上的合力才是使物体移动真正有效的力量。

#### 公司文化的力量
其实最后 Rogo 升职之后，开始考虑公司文化上的问题了，他看到了不只是从前工厂里的产品线，而是整个公司的组织与流程，他意识到：瓶颈也是无处不在，管理也是无处不在。

### P.S.
恩，TOC 是个好东西。

***
首发于 Github issues: https://github.com/xizhibei/blog/issues/44 ，欢迎 Star 以及 Watch

{% post_link footer %}
***