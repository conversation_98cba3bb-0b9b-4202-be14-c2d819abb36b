---
title: 应用配置的几个原则
date: 2017-08-28 15:30:53
tags: [配置]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/56
---
<!-- en_title: principles-about-app-configuration -->

之前在 [《应用配置管理实践》](https://github.com/xizhibei/blog/issues/41) 中提到过配置的管理方式，这次我专门来说说在工程实践中总结的一些应用配置原则。

### 简单友好原则

我们先回顾下一些常用的配置文件格式

> ini，xml，json，yaml，toml

从 ini 开始，方便好用，但是无法支持复杂些的数据结构，之后是 xml ，但是配置文件太庞大，导致可读性也不好；json 的机器可读性更好，但是不支持注释，而且灵活性不高，一旦多加了个逗号都能导致加载失败；yaml 是随着 ruby 的发展而逐渐发展起来了，是一种非常优秀的配置格式，数据结构丰富，允许层级以及注释，只是同样是格式太严格，容错率不高，toml 灵活性较高，有着 yaml 的优点，只是不足之处在于还比较新，使用者少，而且支持的软件也不多。

不难发现，它们的可读性越来越强，容错率越来越高，支持丰富的数据结构，当然，规则也越来越复杂，并且目前主流软件的配置都在向 yaml 靠拢，于是对于系统管理人员来说，便越来越受欢迎。

所以，用 yaml 或者 toml 吧，它们是目前能满足你配置需求的最佳选择。

### 可动态重载原则

一般来说，在 Linux/Unix 下的软件，我们会使用以下几种方式来重新加载配置：

-   SIGHUP 信号：使用 `kill -SIGHUP {PID}` 来触发重新加载操作；
-   专门的接口：对于网络服务型的应用，我们也可以用比如 `POST /reload` 来触发重新加载，[Prometheus](https://github.com/xizhibei/blog/issues/54) 就是使用 `POST /-/reload` 来重新加载配置的；
-   文件的 hash：实时或者周期性监测配置文件的 hash 来判断是否需要重新加载，logstash 就是这么干的；
-   强一致性数据库：就是将变化非常频繁的配置加入到 etcd/zk/consul 等强一致性数据库中，在服务注册发现中会使用到；

这个功能非常有意义，因为不是每次重新更新配置都需要重启应用。

只是，这对于开发者的要求会比较高，因为得考虑到配置的重新加载，而有些模块的重新加载会很危险，就比网络应用中，重新加载数据库，会导致应用暂时无法提供服务。并且，有些模块会有繁杂的依赖问题，如果设计成动态重新加载的话，会导致软件架构非常复杂。

因此，有些时候，我们会考虑宁可简单处理：重启应用。

顺便提一句，在 k8s 中，我们使用配置的最佳方式莫过于将配置放进版本管理，也做成 Deployment 的一部分，将配置变动当做一次应用的升级。

具体来说，我们在 k8s 中会使用 ConfigMap 或者 Secret 的方式管理，然后在 Deployment 中，将配置当成 Volume 挂载到 Container 中，那么，这时候，我们可以将配置文件的 hash 加到 Deployment 的描述中去，一旦 hash 更改，Deployment 就会更改，k8s 就会自动使用滚动更新的方式来更新应用。这么做还有一个好处便是，一旦配置错误了导致应用无法提供时候，k8s 也会拒绝升级，避免了线上应用出错。

### 吝啬配置原则

如果你的程序中，有大量可配置选项的存在，你就该思考，是否有必要有那么多的配置，假如你是这个软件的用户，你会希望配置那么多的选项吗？

在实践中，我们认为，应当使用尽可能少的配置，如果一些配置能够自动检测的话，就不要劳烦用户去配置了，或者，大部分配置给出默认或者推荐选项即可。

另外，如果某个配置是必须的，不存在便会让程序无法启动的话，这时候考虑也要加个默认配置，尤其是在团队开发的情况下，更新代码后应用无法启动是会让我们发飙的。

### 依赖倒置原则

我觉得我们使用配置的意义就在于：**给用户最大的自由度**，用户可以自由地使用配置来组装一个系统，或者说使用配置去模块编程。

这同时也是一种设计的原则：**依赖倒置**，我们不可能对每一个用户都给编译一个单独的镜像，依赖她们的需求来设计我们的系统，那么最佳的做法便是做好所有的模块，然后交给他们自己去配置即可。电脑组装就是个例子，所有的模块都是标准的 (主板、CPU、内存、硬盘等)，用户只需要根据自己的需求与预算挑选合适的模块，然后就能组装成一台自己想要的电脑了。

就比如创业初期，我们不想给每一个运营人员每次都单独拉数据，那么，将所有数据同步到 ES 里面，他们可以学习下如何使用 kibana 的图表，然后就可以定制自己的需求了，这就是将依赖给倒置了。-

同时，对于业务开发的工程师来说，一旦产品说要**写死代码**，就意味着我们必须将相关的代码加入可配置选项，因为有经验的工程师都知道这是个不靠谱的产品，他以后肯定会反悔，然后死乞白赖地求你改功能的。于是等那天来的时候，你就告诉他，你可以今晚通宵加班赶工，明天早上就能上线，但是想吃高级日料外卖，而其实你只是打算看一集冰与火之歌，然后下班的时候改个配置🙈。

### Ref

1.  <https://stackoverflow.com/questions/37317003/restart-pods-when-configmap-updates-in-kubernetes>
2.  <http://blog.zengrong.net/post/2360.html>
3.  《Unix 编程艺术》


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/56 ，欢迎 Star 以及 Watch

{% post_link footer %}
***