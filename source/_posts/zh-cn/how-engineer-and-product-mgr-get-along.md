---
title: 工程师与产品经理相处之道
date: 2017-10-22 17:27:49
tags: [企业文化,工作方法,沟通]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/60
---
<!-- en_title: how-engineer-and-product-mgr-get-along -->

这周去参加了 QCon，涨了不少姿势。但是最有收获的反而不是纯技术方面的，而是与技术相关的内容。其中最有意思的是 @邱岳 的『怎样构建产品和技术之间的合作氛围』，那天本来想听听别的场次的技术内容的，但是一看一个熟悉的名字『孙志岗』，这不是大学老师么？那是我拿到最高分课程的操作系统老师，太巧了，于是就奔着老师去了。他是这个场次的出品人，听了之后，觉得很受用，非技术出身的讲师反而在演讲反面更有优势：口才更棒，演讲更吸引人。

我们在公司里面，总是会有很多的沟通问题，尤其是『程序猿』与『产品狗』，在很多时候，我们容易互相为敌，形成水火不容之势。而在这时候，双方最开心的莫过于看对方在公司里出丑。那么，我们都知道互黑开玩笑没事，但是一旦真的有矛盾，就容易影响公司氛围，破坏合作了。

那么，如何解决这个问题？接下来，把 @邱岳 的演讲内容简单复述如下，并加些我自己的感悟，会对原来的内容会有些修改。（可能有些地方记错了，之后再修改）

### 为什么产品与开发总是容易互相为敌

很显然，两者的思维方式不一致：产品总是在思考 Why 的问题，而开发总是在思考 How 的问题，在两者的交集中，才是我们的 What，或者说 PRD 与产品。

而在我们传统的开发流程中，需求总是从需求方来，到产品经理产出 PRD 之后才会交到开发手里去实现。这就导致了两者之间的机械化流程以及沟通方面的单一方向流动，这是不可避免的。

### 如何才能解决困境

#### 互动有无

产品需要理解开发的 How，即学习开发的技术内容，不是说非要去跟开发一样工作，而是得了解技术语言，当开发说出一个技术术语的时候，你得了解大概是什么东西。同时，得把自己 PRD 中的价值详细交代给开发，而不是简单把 PRD 扔给开发：我相信优秀的开发总是会让开发真正了解到需求中的价值，有句话分享给大家：**『在让别人建造一艘船的时候，首先要做的就是激起他们对大海的向往』**。

对于开发来说，你得理解业务术语，产品经理经常说的业务内容是什么意思，有什么价值，同时将技术的实现给产品说说，让他们也涨涨姿势：这有一个非常明显的好处，产品了解到技术实现的过程与难度之后，一方面他会尽量帮你争取更多的时间去完成，另一方面也会在以后的新相似需求中，给你预留更多的完成时间。

另外，数据埋点细化需求能帮开发理解用户，因为这时候开发能看到需求，方便开发以产品的角度去加深对产品本身的认识与理解。

在制度上，开发与产品可以制定相应的流程，来经常性地互相分享知识。

#### 设身处地

在我们传统的开发流程中，产品会组织可行性会议，来召集开发来开会。但是，这有个问题：会议总是**正式**的，因为这时候的需求已经意味着需要实现了。因此，产品可以做的就是，尽早将需求**非正式地通知给开发**，比如一起吃饭的时候，一起上下班的时候，任何只要是非正式的时候都可以。这样，对于你来说，能增加开发对你的好感，容易养成兄弟情谊，而开发也会有比较强的 **『参与感』**。

另外一种方式就是，参与技术问题的解决，比如总会有 BUG 出现，那么当影响到用户的时候，你就可以帮开发去帮忙跟运营或者客服去沟通，说明问题，让开发可以专心解决问题。这里需要说明的是：不要『暴力沟通』，即不要使用『暴力词汇』：『怎么又出 BUG 了？』，『怎么又是你？』，『是不是你写的 BUG ？』。相信我，这也是为了你的人身安全考虑 :P。

对于开发来说，积极参与业务会议也是一种能够加深理解产品以及业务的方式，虽然有时候会浪费时间，但是，以后你总是会需要了解到具体业务需求的。

在制度上，产品与开发可以轮流当 PM，加深合作，也有助于互相理解。同时，新的需求，最好是同时到达开发与产品，这样的话，产品与开发就可以省点时间去沟通细节，也会有一同作战的『参与感』。

在这里，我也想说，我们常说要让公司里面的每个人都要有**主人翁精神**，其实，**让开发尽早参与到需求的制定，就能大大提升他们的主人翁意识**，让他们更积极对待需求，拥抱变化，甚至能让他们主动帮产品完善需求，同时减少出错，更早并且更好得完成需求。

#### 严于律己

我们常说要严于律己，宽于待人，但是往往在现实中总是容易反过来。

因此，对于产品，**不要代替开发去估时**，这是大忌，这会造成的一个后果就是『你这么牛逼，你咋不自己去实现呢？』。产品有自己的专业领域，替开发去做他们做的事情，是一种非常不专业的行为，易招致反感。

另外，『克制变更』就没什么好说了，开发最痛恨的莫过于『那个，XX 啊，需求有点小改动』，分分钟都会让开发暴走。

对于开发来说，负责任是会被每一个产品都肯定的品质了，上线除了问题，开发不能瞒着，更不能大家知道了之后还逃避责任，找借口。同时，出错了第一次，没关系，大家也不会过分责备，而且文化氛围良好的公司，甚至会『鼓励犯错』，只是，不能再犯第二次错误，因为这涉及到责任心了。

『拥抱变更』也是没的说，需求总是会变的，当产品那么尽心克制变更的时候，你就怜悯下他们吧 :P。

制度上，**对于长开发周期的需求，不要给需求定死交付时间**，开发最痛恨的另一点就是，定一个定死的日期上线，然后倒推需求完成时间，这是一种不尊重也是不负责的表现。我们更想的是：『弹性交付』，给产品预留足够的时间可以将产品做得更好。

这里补充下自己的想法：显然，这可能不是敏捷开发流程，因为在敏捷开发流程中，我们会强调承诺的重要性，将任务拆分足够细之后，我们会能更准确估计开发时间，只是也是不强制交付，我们会要求开发人员分析延期原因，并在下次迭代中调整估时，同时在之后的迭代中，努力达到越来越准确的估时。

#### 建立信任

信任对于开发跟产品来说，是必须的，不然会造成协作上面的种种问题，『文档驱动开发』就是个明显的例子。

因此对于产品来说，有准备的试错，即准好前期的调研工作，将会大大减少开发中的需求变更次数，同时也会让开发认识到你的专业能力。及时通报需求的进展也会让开发有准备，也会让需求更快被开发理解。

不知道你的公司是否也有 KPI，如果是的话，一般来说，你是不会背技术问题的 KPI 的，因此，不妨承担起责任，即帮开发『背黑锅』，也是一种能赢得开发信任的讨巧方式。

然后，当需求顺利上线之后，你是否会想到给开发们发红包呢？其实还有一种非常有效果的办法：给开发向上争取利益，这对于你来说也就是写封邮件表扬他们而已。

当开发知道，你主动为他们背黑锅，并为他们积极争取各种利益的时候，他们有什么理由不帮你好好完成需求呢？

而对于开发来说，不评判需求的价值是你可以做的，毕竟这属于产品的专业范围，而且需求往往是从上层来的，然后到了产品手里，他们也改变不了什么，他们可以帮助你了解到产品的价值，但是请你尽量不要以自己的角度去评判。

对于具体的需求排期，承诺具体交付时间尽管是挺纠结的，但是在产品已经帮你争取到足够多的时间之后，你就忍忍承诺个具体时间把。

另外呢，如果有些需求细节是产品没有想清楚并且是你的举手之劳，你可以帮着去完善，或者直接帮着实现了。这是个非常有意义，也是可以帮助你赢得产品信任的方式。

制度上，产品与开发可以互相背 KPI，可以让他们形成一个利益共同体；同时，为了防止产品与开发互坑，可以将产品与开发长期安排到同一个业务领域中；还有就是适当『追责』，适当批评就行，不要在之后一直提起这个问题，罚钱更是没有必要，（当然了，罚钱如果是用来请大家喝个下午茶倒是不错的）；还有个很取巧的方式，便是制造外敌，我们常说『不有外患，必有内忧』，因此，制造外敌之后，能让公司内部一致对外。

### P.S.

其实产品与开发本就是共同体，『本是同根生，相煎何太急』。我们没必要互相为敌：努力增进沟通，加深理解，能让我们更好更快地完成任务，达成双赢，或许，还能变成一生的好朋友。

### P.P.S. 关于 QCon 演讲 PPT 下载地址

<http://ppt.geekbang.org/qconsh2017>


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/60 ，欢迎 Star 以及 Watch

{% post_link footer %}
***