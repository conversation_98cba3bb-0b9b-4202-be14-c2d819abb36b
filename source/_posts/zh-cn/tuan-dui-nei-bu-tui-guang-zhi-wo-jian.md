---
title: 团队内部推广之我见
date: 2017-05-22 22:32:11
tags: [DevOps,工作方法]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/48
---
一些经验较少的管理者或者项目经理在团队内部推广新技术、新工具的时候，往往，会遇到阻力。

在这里，先讲一个小故事：

> 小 A，在一个传统互联网公司，刚上任他所在的小团队开发技术经理不久。为了提高公司技术团队的工作效率，最近学习了不少 DevOps 有关的技术与文化，想在团队以及公司内部推广起来，于是他组织了一场分享会，蒙着头花了不少时间去做 PPT，分享的那天，来了不少周围的开发以及运维同事，过程中小 A 激情满满，会后大家也表示应该好好学习与实践。但是，之后发现大家真正落实的情况很少，大部分还是继续着之前的做法，一直不变。小 A 很气愤：明明这是一项非常牛逼的技术 / 工具，为什么这帮人不好好实践呢？于是变得很气愤，抱怨大家的不支持以及不作为。

我相信你如果想在团队内部推广的时候，往往也会遇到类似的情况：**理想很美好，现实很残酷**。

如果小 A 太年轻而没有经验，他往往会很沮丧，毕竟这是对大家有好处的东西，推广难度为什么那么难。

### 原因

我也经历过类似的过程，我把原因归结为如下几个：

#### 急于实施

按照信息论的观点，你在推广这些新技术，新文化的时候，大家不一定有着跟你同样的信息量，或者说有些人的信息量甚至比你更多些。

另一方面，你其实对这个技术、文化，并不是那么的了解，导致你向大家推广的时候，你的实践经验并没有得到大家的认可，你会缺乏实践经验去帮助同事们去实施以及提高。

#### 缺乏支持

需要肯定的是，小 A 的做法还是正确的：他在这个过程中，把大家集中在一起分享，告诉大家这是个好文化，而不是通过自己的权力直接命令手下的同事改变。

那么，如果小 A 事先去跟领导以及一些老同事去交流，一个一个的听取他们的意见，补充自己的不足，并让他们提前了解到这个东西的好处，在这个过程中取得大部分权威人员的支持之后，情况会好很多。

#### 利益冲突

小 A 所在的公司，是传统的开发与运维分开的技术团队，开发只负责开发，一旦开发测试完毕，就交给运维去部署，然后只管回家睡大觉。

很明显，对于开发同事来说，如果让他们来负责一部分上线运维工作的话，他们会非常不乐意，毕竟如果上线有问题，他们就会非常难受：一方面需要对上线事故负责，另一方面，本来他们只管在家睡大觉就行了，何必那么痛苦呢？

而对于运维同事来说，痛苦归痛苦，但是开发来负责上线了，是不是要来抢我的饭碗呢？

其实，小 A 要做的是让同事们明白，推广这个文化，对公司以及大家长远来说都是有好处的：

开发会对自己的代码更负责任，因为他们直接面对了用户，能够及时发现自己代码的不足之处，也能够快速定位并且修改 bug，而不是等运维同事把问题反馈了之后才慢慢修改，中间去除了沟通成本。

运维能更专注于公司运维基础平台的维护，不用为线上发布问题头疼，也不用傻傻等着开发修改 bug 才能恢复线上服务的部分功能，甚至在这个过程中跟开发扯皮，互相指责对方。

而对于公司整体来说，小 A 提倡的 DevOps 能够帮助公司节约成本，提高工作效率，减少线上事故的发生次数以及处理时间。只是，这种文化需要公司整体的支持，一个人起的力量是很弱小的。

### 改变环境

上面简单说了一些，这里说说，心态上的问题。

小 A 最后的心态不好，埋怨解决不了任何问题。同样的，我们需要明白，当向现有的团队引入任何变化的时候，大家都是比较谨慎的，毕竟如果这个东西实施之后变得不好，浪费时间精力还能忍受，万一把公司搞乱套，甚至影响线上服务的话，还不如维持现状。

因此，我认为，改变自己的心态最重要，静下心来，慢慢提高自己，然后慢慢去改变周围的同事，让他们深切体会到现有的痛处，以及能得到的好处，从而赢得他们的支持。

然后呢？慢慢改变领导者的心意，最后改变公司里的文化环境。

其实就那么简单：**改变自己**，这是最容易的，但也是很难意识到的。

只要小 A 能一步步坚持自己的想法，不抱怨，静下心来，一步一步去改变，成功就会越来越近。

插一句，为了静下心来，不妨背一遍荀子的《劝学》：**『蚓无爪牙之利，筋骨之强，上食埃土，下饮黄泉，用心一也。蟹六跪而二螯，非蛇鳝之穴无可寄托者，用心躁也。』**

戒骄戒躁，规划好自己的目标，打好基础，慢慢布局，一步步去实现。

### Reference

1.  <https://zh.wikisource.org/zh-hans/%E8%8D%80%E5%AD%90/%E5%8B%B8%E5%AD%B8%E7%AF%87>


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/48 ，欢迎 Star 以及 Watch

{% post_link footer %}
***