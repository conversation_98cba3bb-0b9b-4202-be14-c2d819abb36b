---
title: OKR 实践
date: 2019-07-01 16:27:41
tags: [企业文化,工作方法,管理,鉴书]
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/111
---
<!-- en_title: the-practice-of-okr -->

最近看完了《OKR 工作法》以及《这就是 OKR》，再加上团队实践 OKR 有小段时间了，趁着热度还在，赶紧总结下。

顺便简单评价下这两本书，《OKR 工作法》整体不错，用茶叶商的故事娓娓道来，内容也很有指导性，而《这就是 OKR》整本书最有价值的地方就是它的附录资源，书的内容粗读即可。

### 简介

OKR 就是 Object 与 Key Result 的缩写，即目标以及关键结果，前者表示我们要达到的目的，而 KR 表示能够支撑 O 完成的关键指标。

目前介绍 OKR 的资料很多，比如国内比较早的明道，他们官网博客中有不少的资料，比如[【视频】三分钟理解 OKR](https://blog.mingdao.com/4607.html)。

OKR 可以说是一个管理经验，或者说管理方法论的发展过程 <sup>[1]</sup>，它目前已经是前人管理经验的集大成者了。

历史上，在 50 年代诞生了 MBO (Manage by Object)，即**目标管理法**：用目标来管理员工。而这之后的 80 年代，经过那个著名的偏执狂，时任 Intel CEO 的 Andy Grove 引入以及改善后，正式成为了 OKR。也就是我们目前看到的经典形式，O 与 KR 的规划，以及明确表示 KR 不多于 5 个。<sup>[2]、[3]</sup>

最后，在 1999 年，Intel 的一个前高管约翰 · 杜尔 (John Doerr，传说中的风投之王) 在投资谷歌之后，他将 OKR 介绍给了谷歌的 Larry Page 和 Sergey Brin，得到了他们的认同（实际上，那时候的谷歌也没有其它比较好的管理方式，他们觉得可以试一试）。于是，我们看到了现在的谷歌在 OKR 的帮助下，逐渐壮大，以及做出了一系列优秀的产品。

之后随着 Linkedin、Twitter、Sears、Zynga 等在公司相继开始用，OKRs 在硅谷渐渐盛行起来。

直到了 2014 年，国内的公司如小米、知乎、明道、豌豆荚等等，都开始实施 OKR，就连这一两年处于风口浪尖的头条也是 OKR 的使用者。<sup>[1]</sup>

### 几个例子

在《OKR 工作法》中，有个很有代表性的例子：

> O：向餐厅供应商证明我们提供的优质茶叶的价值
> KR1：客户重复订购率达 85%
> KR2：完成 25 万美元的交易
> KR3：20% 的重复订购客户能自助完成

我们可以看到，目标比较笼统，但却有指导性与感染力，它告诉我们应该达到的目的地，也会让我们知道这个目标的意义，而事实上，O 最好是能引起我们工作激情的目标：**在带领团队建造一艘船之前，先要激起队员们对大海的向往**；同时，下面的三个 KR 告诉我们这个目标需要有着三个关键结果的支撑，也就是将目标转换成了具体的可测量指标。

另外一个例子来自于明道的博客<sup>[4]</sup>：

> O：使产品的访客到留存的转化比率提高到 5%
> KR1：改版注册流程，提高注册转化率到 30%；
> KR2：提高 App 的 30 天留存率到 45%；
> KR3：上线 HR 应用。

这个例子可能更有互联网公司的代表性，对于产品或者运营来说，这个 O 与 KR 可以完全用来指导下一个阶段的计划以及任务了。

### 如何实施

其实 OKR 很容易理解，作为任何创业团队或者其他公司，都会有自己目标、以及计划，只是由于使用的工具不对，或者没有方法论的指导，很容易陷入误区。

或者也没有良好的团队文化，目标一直只是管理层的目标，与员工无关，所有的战略目标，到了员工这个层面，只剩下冷冰冰而又难以理解的 KPI。

那么现在回到 OKR，对于一个刚接触到的团队来说，比较合适的方式是采取先用短期目标试行的方式去慢慢适应。

比如我们可以先定一个 1~3 个月的公司目标，对于互联网公司来说，就可以是在 『3 个月后的最后一天，产品上线』这样的目标，然后，跟上下级同事一起商量这个目标的指标。

比如第一个 KR，产品上线就行了么？用户不买账怎么办？那么我们是不是可以采取敏捷开发的方式，争取能够多次迭代呢？那么，KR 就可以是：

> KR1：三个月内，通过产品的多次迭代的方式，达到产品基本可用。

等等，这个 KR 似乎不可测量，多次是多少次？基本可用是什么意思？那么我们通过商量，可以修改一下：

> KR1：三个月内，通过产品的 6 次迭代，达到产品基本可用，在最后一个月，用户日活达到 1000，月活达到 10000。

然后这个 KR 就变得可实施了，剩下的 KR 同理，通过与团队共同商量制定，这里特别要注意所有目标以及关键结果必须要一起商量得来，而不是从上而下的命令，团队可以针对这些 KR 进一步分解成里程碑与任务，最终形成 Road Map (即路线图，什么时候做什么事情的规划表）。

执行的过程中，可以按周来定时开复盘会议，来更新 OKR，比如在上一周的执行过程中，遇到了什么问题，导致 OKR 无法继续，或者新的市场形势非常乐观，之前设定的 KR 太低，都可以在复盘会上进行调整。同时，每个月月底的复盘会议上都需要对这个月的 OKR 进行打分，0.5 分表示基本合格，0.3 表示不及格，但同时说明目标太难，需要作出达不到目标的解释，0.8 超出预期，但同时也说明目标太过于简单，最后，一个 0.7 的目标接近于完美，表示这个目标经过了充分拉伸，是一个挑战了自己之后努力达到了的目标。

几个容易出现的问题（其实是我们出现过的问题）：

1.  KR 应该包含几个部分：策略、产出以及对产出的衡量，没有策略只有结果是典型的老板思维：『不管过程，只要结果』，不利于目标的实施与推进，实属管理大忌；
2.  可测量的意义在于，对团队保持绝对的坦诚，所有的结果与问题放在台面来说，避免互相猜测与沟通问题；
3.  关键结果应当完全支撑目标，不能是关键结果达到了，目标却没有达到，这说明关键结果制定不合理，或者是目标本身不对；
4.  处于探索阶段的目标，可就以探索本身作为目标，关键结果以给团队信息同步的方式来制定，比如就可以是团队成员对分享的结果的满意度；

### 与 KPI 对比

之前有在团队中实施过 OKR 的朋友，告诉我：『这个与 KPI 差不多啊，我们实施前后没什么区别』。细问才知道，他们是把 OKR 当做绩效考核的工具了。

其实，它们之间最关键的区别在于是否将 OKR 的结果作为绩效考评的根据，一旦作为根据，那确实与 KPI 无异了，因为这就将 OKR 的精髓毁掉了：**坦诚的企业文化**。对失败要有足够的容忍，鼓励创新与坦诚，这也是之前我在 [如何理性地失败：黑匣子思维](https://github.com/xizhibei/blog/issues/106) 中提到过的：因为害怕结果与自己的工资升职挂钩，所以会害怕失败、不坦诚，将 KR 故意设低，并且在回顾会上也会隐瞒对自己不利的信息，导致下一个 OKR 制定完全偏离方向。

那，实施 OKR 就无法考核绩效了？不用担心，因为整个实施的过程中，一个非常重要的原则就是**目标透明与绝对坦诚**，所有的关键结果都会有证据作为打分的基础，整个团队都会对身边的人非常了解，谁对团队的目标做了最大的贡献，在复盘会上我们一目了然。

《这就是 OKR》中也提到了 CFR 以及 360 度评估等作为绩效考评的手段。

另外，这两者并不是水火不容，具体在执行层面，比如销售团队，完全可以在指定完成 OKR 之后，制定行动指标 KPI 来保证 OKR 的实施。比如每天联系多少多少客户、每周完成多少客户的签单等等。

### 与敏捷的关系

OKR 只是一个目标的管理方式，具体实施过程中，可以与敏捷开发完全结合起来使用。

前面说到，我们会将关键结果拆成多个 Epic（史诗，即包含多个用户故事的大用户故事）以及里程碑，而这个过程中，我们就会用每一个敏捷的迭代过程去完成里程碑里的任务以及用户故事。

相对于之前的随意添加用户故事，我们在 OKR 的指导下可以更有方向性地制定开发迭代计划，而且对于不同故事的优先级也会更容易判定。

最后，对于使用 Jira 以及 Conference 的朋友，Atlassian 也提供了 OKR 模板，可以考虑放弃 Excel 了。<sup>[5]</sup>

### Ref

1.  [OKR 如何彻底激发员工积极性，挑战不可能？][1]
2.  [技术漫谈：为何 KPI 毁了索尼，而 OKR 却成就了谷歌？][2]
3.  [创业公司如何用好 OKR][3]
4.  [从理论到实践，让你全面看懂 OKR！][4]
5.  [Atlassian: OKR (Objectives and Key Results)][5]

[1]: https://www.infoq.cn/article/gU*236UQhE5FYgDsYXoB

[2]: https://www.infoq.cn/article/kpi-ruined-sony-while-okr-accomplished-google

[3]: https://www.infoq.com/cn/articles/how-to-make-okrs-actually-work-at-your-startup

[4]: http://blog.mingdao.com/3934.html

[5]: https://www.atlassian.com/team-playbook/plays/okrs


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/111 ，欢迎 Star 以及 Watch

{% post_link footer %}
***