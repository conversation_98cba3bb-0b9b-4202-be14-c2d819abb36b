---
title: 穷查理宝典：富有与智慧并存的老头子查理·芒格
date: 2020-05-04 23:08:08
tags: [读书,鉴书]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/138
---
<!-- en_title: poor-charlies-almanack-a-rich-and-wisdom-old-man-Charles-T-Munger -->

这周暂时不写 CMake 的内容，换换口味，说说一本非常值得推荐的书《穷查理宝典：查理 · 芒格智慧箴言录》。

### 简介

作者是彼得 · 考夫曼，翻译者李继宏。

先来简单说下不足的地方：整体翻译欠佳，读起来会觉得比较絮叨，排版上也有不少问题，查理十一讲的内容有不少重复的地方，并且每一讲本身也有重复的地方，会让我觉着非常啰嗦。对于没有读过本书的朋友，重点读**第二章　芒格的生活、学习和决策方法**以及**第四章查理十一讲**，其它部分略读即可，当然，结尾处的书单记得抄下来。

至于好的地方，也就是查理 · 芒格本人的一些思想与箴 {zhēn} 言了，在本书中也会不断被重复提及。

另外，给不知道查理 · 芒格这个人的朋友，也简单普及下，他跟沃伦 · 巴菲特是伯克希尔 · 哈撒韦的代表人物，大家会对巴菲特熟悉一些，但是巴菲特背后的男人，就是查理了。什么？股神巴菲特也不知道？那只需要知道他投资炒股特别厉害就行了，并且特别特别有钱，他跟查理目前都是八九十岁的老头子了，却依然非常精神（不过今年 2020 巴菲特股东大会上，查理因为健康问题没有出席，毕竟 96 岁了）。

或许你在平时的阅读中，已经接触过了查理的一些思想了，比如那句：

> 我只想知道将来我会死在什么地方，这样我就永远不去那儿了。

以及：

> 如果你想获得某样东西，那就要让自己配得上它。

其实，这两句是我在看这本书之前就知道的两句话，只是没有去了解过是谁说的。

下面，我就要开始剧透了，挑几个自己感触比较深的思想来说说。

### 查理的剑

之所以说是剑，是因为书里面提到过的一句话：**我的剑只留给能够挥舞它的人**。

先来说说上面两句话，**我只想知道将来我会死在什么地方，这样我就永远不去那儿了** 说的是逆向思维，而**如果你想获得某样东西，那就要让自己配得上它**说的是提高自己，而不是奢望天上掉馅饼。

然后我要说的还有**拥有跨学科的心态，炼成自己的思维框架**，也就是多学科的思维。

最后简单说下**检查清单**以及**固守能力边界**。

#### 逆向思维

其实也就是把所有可能导致自己失败的路径找出来，然后不去走就行，顺着这个思路，我也可以写出一段话送给大家。

我不知道如何成为技术大牛甚至业界领袖，但是我知道如何才能不成为：

1.  不要运动，每天下班后直接躺着刷抖音，专门刷那种沙雕视频，小姐姐视频，一刷就是 12 点，然后睡到第二天很晚才起来；
2.  不要学习新知识，对于所有的新知识，都在可以评论的地方抱怨学不动了；
3.  不要看书，更不要学习前人的经验，自己不断摸索的路才重要，你总能找到捷径去解决问题，然后更不要总结经验，写博客这种浪费我时间的事情更是不可能；
4.  不要重视工具，多几次复制黏贴就能解决的事情，浪费点时间有什么关系，造工具是不可能的，这辈子都不可能；
5.  不要对上级布置的问题多问几个为什么，干就对了，上级要什么就是什么，说什么你的回答都是『好的，我知道了』，不要用你自己的话重复一遍，做任务的过程中也不要汇报进度，直到 Deadline 了才可以说有问题要推迟；
6.  不要追求问题背后那些东西，能完成任务就得了，那些 TCP/HTTP 协议、工具、数据库以及框架的底层原理、架构设计等，跟我完成当前的任务没有关系，不如省出时间去刷抖音、玩游戏；
7.  当团队遇到有挑战的事情了，不要站出来，站出来就要承担责任了，老板不会发加班工资，奖金就那么点，做失败了还要罚钱，我涨了经验又不能换钱；

我还能写出一大堆，相信你也可以。

#### 配得上

这句话可以用在很多的地方，另一半、新工作岗位、工资、奖金等等。

有时候跟朋友聊天开玩笑吹牛，我们会有不切实际的意淫，比如妄想会有一个白富美女票，然后这辈子都不用奋斗了。

但事实呢？换个角度去思考，你就会明白，假如你是一个白富美，你愿意嫁给你这样的人？正所谓，**你配得上？**。

多骂自己几句，等清醒了，就会明白没有所谓的天上掉馅饼，让自己得到好东西的方式，只有一条路，那就是让自己配得上。

喜欢一个比自己优秀的人？让自己配得上对方；想要涨工资，以及丰厚的奖金，多贡献，让自己配得上那个数字；想要一份更好的工作，努力学习、站出来承担更多的责任，涨更多的经验，让自己配得上那份更好的工作。

#### 跨学科

查理非常激进地认为，现代大学教的知识太偏科，我们需要更普适的教育，因为他本人也是跨学科学习者，通过学习不同学科的思想，收获了知识以及财富。

对于我们来说，我们需要主动去学习其它学科的内容，从而可以以多个角度去思考。

比如对于工程师，我们不仅仅需要计算机本身的知识，还需要学习经济学、心理学的思维。另外，还需要其他部门的知识，比如运营、产品、设计，这些看似无用的知识，能帮你在职业生涯中后期提升到新的高度，也就是打破只研究技术的天花板。

当然，这里说的其它知识，并不需要你完全掌握它们的全部，你只需要花少部分时间去掌握其中最重要的思维模型即可，我们的目的是形成自己的思维框架。查理就掌握了至少多个学科 100 多个思维模型，而一旦多个思维模型一起使用的时候，就会碰撞出不可思议的结果，即它们相互之间的作用相乘放大，也就是查理说个不停的 **lollapalooza** 效应。

比如，技术搞到一定程度，尤其是架构这个级别，你会发现经济学思维很有帮助：架构方案对比中，必然会涉及成本的估算，这个成本不仅仅是人力物力等资源，还会有**沉没成本、机会成本以及边际成本**。而一旦你对这三个成本了解加深之后，你就会对你们公司的业务有更深的理解，反过来，也会帮你在架构上有更好的决策帮助。

心理学就更不用说了，举几个向领导提需求的方式：

1.  **登门槛效应**：先抛出一个领导肯定会答应的事情，再抛出一个领导会犹豫的事情后，你会发现这比你之前直接抛出那个会犹豫的事情，领导拒绝前一种方式的概率会低很多（这条书中没有提到，我自己加的）；
2.  **回馈倾向**：是上面说的另一条路径，先提出一个领导肯定不会答应的事情，等领导拒绝后，再提出一个退一步的要求后，领导答应的概率会大大提升；
3.  **剥夺超级反应倾向**，简单来说就是捡到 100 块钱的快乐补足不了丢掉 100 块钱的痛苦，所以可以尝试不要跟领导说这样做可以赚 100w，你要说不这样做会损失 100w；

总之，主动去学习其它学科的内容，会帮助你避免『铁锤人效应』，也就是**手里拿着锤子，认为所有东西都是钉子**。当你学习了其它学科的知识后，你就会有其它的『工具』去解决之前不好解决的问题了，这样解决问题的可能性会更高。我们所拥有的工程技术也不是一把万能的锤子，早一些理解这一点，你就能更快地成长起来。

查理在**第十一讲人类误判心理学**说了不少心理学的知识，相信对你也会有不少启发。

另外，我还提到了学习一些运营、产品、设计的知识，这不仅仅是增加你的知识储备，更是能让你与他们有更多的共同语言，能够站在他们的角度去思考问题，也能更加理解他们对于各种问题的处理方式。

总之：

> 把自己变成学习机器。

#### 检查清单

查理与巴菲特投资任何公司之前，都会对着他们累积的投资检查清单进行确认，没有问题了再拿真金白银进行投资。

对我们的启示，我想到了可以应用的地方：就是在发布线上服务之前，进行检查清单确认，有助于大大减少线上服务的故障率，毕竟大部分故障都是人为的。

#### 固守能力边界

这条也被叫做『不懂不投』，即查理如果不理解一个公司以及行业，那么他就不会考虑它们。

但是，这不是绝对的，比如巴菲特与查理之前不投高科技行业，但是却在 2019 年投资了苹果，这正是他们在拓展自己的能力边界。

这也让我想起来[曾国藩](https://github.com/xizhibei/blog/issues/122)，这不就是**结硬寨，打呆仗**么。

对我们来说，找到自己的优势所在，并好好利用这种优势与别人进行竞争，会让自己成功概率更高；同时，也别忘记拓展自己的优势与能力边界。

### 结语

其它的内容限于篇幅与理解，不多说了，查理这个睿智的老头留给了我们不少的智慧财富，这本书重点内容还是可以多看几遍的，建议你也看看。

### P.S. 查理的剑

1.  如果你想获得某样东西，那就要让自己配得上它；
2.  正确的爱，应该以仰慕为基础。仰慕那些对你有教育意义的伟人；
3.  只有学习了学习的方法后，才能有所进步；
4.  不断学习各学科，最重要的知识点，并不断实践它们；
5.  隐藏你的睿智，让别人感到自己是最聪明的人；
6.  拥有跨学科的心态，炼成自己的思维框架；
7.  要想更好地解决问题，请学会逆向思考；
8.  摆脱自私、偏见、嫉妒和自怜；
9.  避免变态的激励机制，与仰慕的人一起工作；
10. 保持客观习惯，核对检查清单；
11. 去成为最有能力，和最愿意学习的人；将不平等最大化，这通常能够收到奇效；
12. 要拥有自己真正的能力，而不是鹦鹉学舌的知识；
13. 做最感兴趣的事，然后努力配得上它；
14. 期待麻烦，让打击和麻烦，成为成长的契机，勤奋、节俭；
15. 文明的最高境界，是利益各方之间，无缝的信任之网；


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/138 ，欢迎 Star 以及 Watch

{% post_link footer %}
***