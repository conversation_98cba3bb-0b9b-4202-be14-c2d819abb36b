---
title: 如何用 PXE 在一小时内安装完 10 台以上的操作系统
date: 2019-10-21 21:28:11
tags: [工作方法,总结,Ubuntu]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/123
---
<!-- en_title: how-to-install-more-than-10-os-in-an-hour-with-pxe -->

上周折腾了一件事，让我觉得还是有必要把过程记录下来的，方便大家之后也要做这件事的时候，可以节约不少时间。

这件事就是：如何在一小时内装完 10 台以上的操作系统？

### 以前我们是怎么做的

首先我们需要同步一下信息：对于新机器的常见的安装方式，我们需要在 U 盘，CD 等储存上刻一个系统，然后从 BIOS 选择从这些存储上引导启动安装程序，然后按照步骤安装即可。这样安装一个系统，从刻操作系统到安装完毕，预计需要半个小时，不熟练慢点就可能是 1 个小时了，再遇到点硬件问题的话，那就是半天起步了。

因此，如果用这种方式，并且要在一小时内装完 10 台操作系统，预计我们至少需要有 5 个人，5 台目前可用的电脑，刻 10 个 U 盘，这样，我们也不能保证在 1 个小时内完成，因为如果安装的人可能不熟悉安装步骤。

显然，用这样的方式，我们完成这个任务的成本太高了，我们把成本降下来，理想的情况是：

1.  一个小时内完成；
2.  一个 U 盘，甚至不用；
3.  一个人；
4.  一台电脑；

好了，在这种苛刻的条件下，如何完成任务？下面就该主角出厂了：PXE。

### PXE 介绍

PXE 的全称是 Preboot eXecution Environment，也就是**预启动执行环境**，
它描述了在启用 PXE 的客户端上，从网络检索的软件程序集引导启动的的标准化客户端 - 服务器环境<sup>[1]</sup>，其实也算是个非常古老的协议，最早可以追溯到 1984 年，人们尝试用 TFTP 协议引导启动，而它一开始是被 Intel 作为 Wired for Management 的部分而引入的（有的文章说它是被 Intel 发明的，似乎说法不准确），它的 2.0 版本发布是 1998 年<sup>[1]</sup>，它包括了以下几个内容：

1.  DHCP 协议，能够让客户端获取分配给自己的 IP 地址；
2.  TFTP 协议，能够让客户端下载系统最小的 OS 启动文件；
3.  OS 相关的文件加载自己的 TCP/IP 栈，开始下载其他文件来启动相关程序，可以是安装程序，也可以是启动系统本身；

目前，绝大多数的主板都能支持 PXE，如果你没有在引导选项中发现，那可能需要在芯片选项中启动，它可能会被归类成 legacy 选项。

对于操作系统，目前所有主流操作系统以及部分的 Windows 服务都能支持 PXE<sup>[1]</sup>。

### 如何使用

现在，我们以 Ubuntu 18.04 为例来说明（因为手头上只有它），如何快速批量安装多台操作系统：

#### 准备

1.  一台 Linux 服务器主机，加上一台显示器，一个键盘，一个鼠标，假定地址为 ***********00；
2.  Ubuntu Server 镜像；
3.  一台路由器，开启 DHCP 服务，我的网段为 `***********/24`，之后你需要根据自己的网段修改；
4.  多台待安装系统的主机，以及对应的多根电源线、网线；
5.  一台显示器，一个键盘，一个鼠标；

如果是笔记本之类，自行调整即可，流程都是一样的；

#### 服务器搭建

首先我们需要在 Linux 主机上进行搭建 PXE 环境

1.  下载镜像

认准这个地址：<http://cdimage.ubuntu.com/releases> ，不要选择 live 版本，目前我尝试过 Ubuntu 18.04 只有 server 可以正常安装。

```bash
wget http://cdimage.ubuntu.com/releases/18.04/release/ubuntu-18.04.3-server-amd64.iso
```

挂载镜像

```bash
sudo mkdir /mnt/ubuntu
sudo mount -o loop ubuntu-18.04.3-server-amd64.iso /mnt/ubuntu
```

2.  安装几个关键的软件

```bash
sudo apt-get install dnsmasq nginx system-config-kickstart -y
```

-   dnsmasp：用来实现 DHCP 分配地址以及 TFTP 协议下载最小 OS 启动文件（其实也有用 dhcpd+tftp 实现的，但这个更方便，配置一次即可）；
-   nginx：用来实现 HTTP 协议下载系统镜像；
-   system-config-kickstart：用来无人值守安装；

3.  准备 TFTP 目录

```bash
sudo mkdir /var/lib/tftpboot
sudo cp -Rf /mnt/ubuntu/install/netboot/* .
sudo chmod -R nobody /var/lib/tftpboot
```

4.  配置 dnsmasq

**注意**，这里由于我们已经有了路由器提供的 DHCP，为了防止冲突，我们需要换成 DHCP Proxy。

```conf
# Disable DNS Server
port=0

# Enable DHCP logging
log-dhcp

# Respond to PXE requests for the specified network;
# run as DHCP proxy，我踩坑的地方之一
dhcp-range=192.168.1.0,proxy

dhcp-boot=pxelinux.0

# Provide network boot option called "Network Boot".
pxe-service=x86PC,"Network Boot",pxelinux

enable-tftp
tftp-root=/var/lib/tftpboot
```

6.  启动 dnsmasq

```bash
sudo systemctl enable dnsmasq.service
sudo systemctl start dnsmasq.service
```

最好确认下状态

```bash
sudo systemctl status dnsmasq.service
```

7.  准备 Ubuntu OS 目录

```bash
sudo mkdir /var/www
sudo cp -Rf /mnt/ubuntu /var/www
sudo chmod -R www-data /var/www/ubuntu
```

8.  配置 Nginx

```conf
location / {
   root  /var/www;
   autoindex on; # 注意这里！！！
   index  index.html index.htm;
}
```

9.  启动 Nginx


    sudo systemctl enable dnsmasq.service
    sudo systemctl start dnsmasq.service

10. 配置 Kickstart

Kickstart，简单来说就是可以帮你自动安装操作系统的配置文件，实现无人值守安装，目前大部分 Linux 操作系统都实现了这个协议。

可以是 UI 方式配置：

```bash
system-config-kickstart /var/www/ubuntu/ks.cfg
```

也可以用 CLI 配置：

```bash
#Generated by Kickstart Configurator
#platform=x86

#System language
lang en_US.UTF-8
#Language modules to install
langsupport en_GB.UTF-8 zh_CN.UTF-8 --default=en_US.UTF-8

#System keyboard
keyboard us
#System mouse
mouse
#System timezone
timezone Asia/Shanghai
#Root password
rootpw --disabled
#Initial user
user --disabled
#Install OS instead of upgrade

user ubuntu --fullname="ubuntu" --password ubuntu_123
# 密码不能太简单，不然会被强制弹出对话框，打断安装过程

#Reboot after installation
reboot
#Use text mode install
text

install

url --url http://***********00/ubuntu
bootloader --location=mbr

#Clear the Master Boot Record
zerombr yes
#Partition clearing information

clearpart --all --initlabel  --drives=sda,sdb

#size单位为M
part / --fstype ext4 --size 1 --grow
part swap --size 2048

network --bootproto=dhcp --device=eth0

firewall --disabled
skipx

%packages
vim
software-properties-common
gpg-agent  # apt-key needs this when piping certs in through stdin
curl
openssh-server
net-tools  # ifconfig and netstat
wget
git
# ubuntu-desktop # 如果要 UI 界面的话
```

11. 修改 PXE 启动配置

```bash
sudo vi /var/lib/tftpboot/ubuntu-installer/amd64/boot-screens/txt.cfg   
```

将其中的这一行：

```conf
 append vga=788 initrd=ubuntu-installer/amd64/initrd.gz -- quiet
```

改为：

```conf
 append ks=http://***********00/ubuntu/ks.cfg preseed/url=http://***********00/ubuntu/preseed/ubuntu-server.seed vga=788 initrd=ubuntu-installer/amd64/initrd.gz -- quiet
```

12. 安装
    现在，将多台机器依次启动，按 F2/F8/F12 等进入 BIOS 程序，选择 PXE 即可不用管了，完全自动化安装。只有一台显示器的情况下就需要不断更换插拔了，在理想情况下直接进入安装程序就可以换下一台了。

### P.S.

实际上 UEFI 也能实现网络安装，这次我选择的还是 legacy BIOS 方式安装，因为能查到的相对的资料多一些，如果大家感兴趣，也可以试试，资料在 
 <https://github.com/tianocore/tianocore.github.io/wiki/Configuring-PXE-Boot-Servers-for-UEFI>

### RFC

1.  [Preboot_Execution_Environment][1]
2.  [网路启动服务器安装和配置方法 (pxe+tftp+dhcpd)](http://web.archive.org/web/20160407052008/http://www.51know.info/system_install/pxe_server/NetworkBootServer.html)
3.  [PXE 网络批量安装 ubuntu](http://web.archive.org/web/20160407183615/http://www.williamsang.com/archives/1842.html)
4.  [网络安装 Ubuntu 14.04 server · GitHub](https://gist.github.com/chenyukang/0f67b9ef0a8c32aa8f95)
5.  [ubuntu+dnsmasq+kickstart 实现快速无人值守批量安装_genstone_新浪博客](http://blog.sina.com.cn/s/blog_5459f60d01016rkb.html)

[1]: https://en.wikipedia.org/wiki/Preboot_Execution_Environment


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/123 ，欢迎 Star 以及 Watch

{% post_link footer %}
***