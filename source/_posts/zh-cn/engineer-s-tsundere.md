---
title: 工程师的傲骄
date: 2021-03-22 20:52:52
tags: [工作方法,创业,总结]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/165
---
<!-- en_title: engineer-s-tsundere -->

最近的几场外部对接，由于我们的业务涉及到对外合作，因此避免不了跟外部工程师的对接，然而对接的时候，就是出现各种幺蛾子的时候了。但是，这个过程中，让我发现了一个很有意思的事情：在群里吵起来的两个工程师，私下聊天的时候却是和和气气的。

我们从典型的外部对接开始说起。

### 典型的外部对接

大致的流程是这样的：销售跟商务接触合作方，双方互相了解需求，如果合适的话，就开始商讨各自去哭，确定如何进行商务上的对接，同时，这时候看双方的合作意愿情况，来决定谁是大爷，谁是孙子。至于判断谁是大爷，从我一个工程师的角度来说，那就是国企 / 央企 > 有政府关系的私企 > 大企业 > 小企业。相信你快看就能看出来，其实就是按资源的掌控程度来决定的。

所以，基本上大爷就只需要提供 API 文档，技术文档就可以了，这些文档会被商务转发到工程师手里，进行初步的技术判断，看看对方的文档能不能提供己方的需求。

当大家基本确定需求了，一起进一个群，同时把双方的工程师拉进群里，这时候，问题就出现了，有些商务比较没概念，直接把没有事先通知详细情况的工程师拉进群，就会导致奇葩情况出现。

首先大家互相介绍，之后对方在群里扔了一个文档，然后己方的商务直接艾特你，于是你很懵地看了看文档，大致明白了对方让你对接。然后问题就来了，如果你这时候说对方的 API 文档不满足需求，甚至提出了修改意见，那对方就会非常傲慢地跟你说：

“其他家都是这样对接的，他们没有问题，怎么就你有问题？“” 你这方案不行，我们不可能为你们专门修改系统的，修改了其他客户怎么办？“
“你们的实施成本高，那是你们需要解决的问题，这跟我们没有关系。“

这时候，你只能憋着一肚子火，简单回一个：” 好的，明白了 “。

以为到这个时候，事情就完事了吗？显然不会。因为后续的流程都必须按照他们的流程走，而这个流程会让你非常痛苦，这不是接口对接了就完成的事情，因为你的公司可能才是卖产品给用户的人，后续一旦有问题，客户肯定第一个找的就是你，如对接服务总是出问题，不稳定，接口出错，于是你必须抽时间来解决这些问题，再次跟他们确认问题所在，帮对接方查找问题。

一旦查到了问题所在，你会发现这时候火药味很可能会很浓，因为出错的一方注定会被「鄙视」。其实不用偷笑，就算作为当事人的你，在心里多少会对对方有想法的。

### 一个反转

看到这里，或许你会认为对方真傲慢，是个不讲理的人。

然而如果你加了对方好友，你就会发现聊天的时候，对方完全变了个样。在群里的时候，他不会那么好说话，甚至会跟你针锋相对。但是一旦到了私下，你会发现对方可能比较好说话，有什么问题也会第一时间给你解答，帮你解决问题所在。

为什么呢？

显然，群是在一个公开的地方，商务销售甚至顶头上司也在，那他所有的话都会变的非常正式，态度强硬，不会让你觉得非常好讲话。但是私下聊天，没有了群里的压力，同为技术人员，但凡有点胸襟，大家都会互相学习，有一种同为打工人，都不容易，互相帮忙的意思。

### 另一个反转

或许你会认为我在与外部对接中是个比较好说话的人，那你又错了。

因为我们公司也有当大爷的时候，而事后才发现，我居然也会非常傲慢。比如一旦对方说看不懂我写的文档，或者不会搭建一些基础服务的时候，我就会认为对方水平不行，懒得理睬。当同事在私下提醒我，说话的时候不要那么冲的时候，才明白自己给对方留下了不好的印象。

关键，我们还不是国企央企之类的，是个小微企业，那就意味着我还不得不给他们解答，为他们修改方案。

这个问题不仅仅发生在我身上，我的同事也会如此。在群里吵架的我也见到过不少，然而那个吵得很凶的同事，现实中却是一个与我们相处很愉快的人。

### 最后

有对接经历的朋友，相信会有类似的体会，明明是挺好相处的一个人，却要在外部对接中，显得那么强硬，我把这种现象叫做「工程师的傲娇」。至于背后的原因，其实很大程度上是自尊心，无法放低自己的姿态。

一旦明白了这其中的缘由，其实就可以进一步反思自己了，有必要跟同为工程师的对方较劲？放低姿态，理解对方，我们才会离成功更进一步。

我也是慢慢体会到：**不要把对接的另一方同行当作同行，而因该当作你的用户，你应该服务好的用户**。于是，你会认为用户他不会使用你的产品吗？或者你会认为用户水平不行，不够格使用你的产品？或许是的确不会用，但更重要的是你没有让他们觉得很好用，假如真这样的话，你就会失去用户，而一旦把对方当作你的用户，心里平和的你，也会更愿意接受对方的意见，更加理解你的用户，从而改进自己产品。

我们也不妨把这个思想推广一下：假如你要做公司内部的产品，比如监控，也需要考虑一点：你的监控不仅仅是为你自己服务的，**把你的同事当作用户**，不要用无穷的监控报警去轰炸他们，考虑下用更好的方案，比如 [Prometheus 以及 Alert Manager](https://github.com/xizhibei/blog/issues/54) 。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/165 ，欢迎 Star 以及 Watch

{% post_link footer %}
***