---
title: 【MQTT系列】（一）简介之 Hello World
date: 2021-08-29 23:43:27
categories: [MQTT]
tags: [MQTT]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/179
---
<!-- en_title: mqtt-1-intro-hello-world -->
<!-- toc -->

### 说在前面

最近两个月，我几乎是停更了，虽然之前提到过不会及时更新了，但是拖了那么久还是第一次。我当然可以用忙来解释，毕竟我周末用来刷 B 站的时间也少了好多。所以就陷入了另一个窘迫的地步：现在有素材可以写，但是这些可以拿来写的素材却是我忙起来之后才能得到，导致没有足够的精力时间去整理这些素材。

好了，话虽如此，博客还是要继续写的，不然积累的经验与知识又会不成体系。

是的，我又准备开个系列了，一方面，系列的文章会显得成体系一些，更能帮助一些刚刚入门的同学，另一方面，这样的话接下来不至于冥思苦想该写什么（好像这才是真实目的）。

### MQTT 简介

MQTT 是非常简单的协议，最初由 IBM 的两位工程师 Andy Stanford-Clark 以及 Arlen Nipper 在 1999 年为监控输油管道设计的。它被设计的场景就是有限的带宽、轻量级以及很小的耗电量，在那个时候，卫星宽带就是那么小，且贵得让人肉疼。<sup>[1]</sup>

到了现代社会，虽然带宽的成本大大降低，但是仍有大量的场景需要用到这种协议，比如，智能家居（其实还是物联网）。许多的小型物联网设备靠着一块纽扣电池需要工作几年的时间，因此 MQTT 非常适合用来当作应用层的传输协议。

总结来说，MQTT 就是一个服务端、客户端架构的发布订阅消息传输协议。它非常轻量、开放、简单，设计上就非常容易实现。这些特性让它非常适合在如机器与机器 (M2M) 以及物联网(IoT) 这样受限于小内存以及窄带宽的领域发挥作用。<sup>[2]</sup>

IBM 在 2013 年将 3.1 版本提交到了 OASIS，而在之后的 2014 年，OASIS 加了很小的改动，发布了 3.1.1 版本。

2019 年，OASIS 给 MQTT 增加了很多特性，比如更好的错误处理、共享订阅、消息内容类型等等，版本也升级到了 5，之后也会用专门的章节来说说这些特性。

### Hello World

首先，你需要有一个 MQTT Broker。首先需要安装 mosquitto …… 嗯？你不知道这是什么？好吧，那就换个更简单的做法。

首先，我们可以用一些公共的，比如国内的 EMQ（杭州映云科技有限公司）提供的 broker.emqx.io（这里必须给国产软件打广告，他们提供的 MQTTX 客户端是我目前用的最顺手的，Broker 的功能也非常强大，我计划专门出一篇来介绍服务端的搭建）。

然后，也是他们家提供的 [MQTTX 客户端](https://mqttx.app/) ，用 Electron 实现的，各平台都支持，直接下载即可。

打开 MQTTX 客户端，我们开始一次简单的测试。

1.  点击左侧侧边栏的 + ，创建一个连接（这个 + ，我认为有点不符合交互逻辑，作为一个创建按钮跟其它的按钮是不同级别的，放在连接列表里面，与新建分组放一起更合理）；
2.  这时候，会跳出一个创建页面，然后随便填一个名称，点击连接即可，如果没遇到网络问题的话，就可以顺利连接上（你看，它们知道你懒，Broker 地址、端口之类的全部帮你填好了，这点也非常值得做技术类产品的我们学习，如何让用户一开始以最低的成本来使用产品）；
3.  现在，我们来创建一个订阅，点击页面中的添加订阅，在弹出的对话框中，填一个随机一点的 topic，比如 `test/907839342134`，毕竟这是公共的 Broker，避免跟其它人冲突，然后点击确定即可。
4.  最后，我们来发布一个消息，在左下角，有个输入框提示输入 Topic，我们就输入 `test/907839342134`，在它下面的内容框里面，输入 `{"hellow": "world"}`，点击它更下面的纸飞机，发送后，你就能看到，接收到了自己给自己发送的消息。

![mqttx](https://blog.xizhibei.me/media/16253875626915/16302244840845.jpg)

好了，这次就先到这，非常简单的介绍，接下来会尽可能详细介绍 MQTT 的一些概念、原理以及实践应用。

### Ref

1.  [MQTT][1]
2.  [MQTT Version 3.1.1 Plus Errata 01][2]

[1]: https://en.wikipedia.org/wiki/MQTT

[2]: http://docs.oasis-open.org/mqtt/mqtt/v3.1.1/mqtt-v3.1.1.html


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/179 ，欢迎 Star 以及 Watch

{% post_link footer %}
***