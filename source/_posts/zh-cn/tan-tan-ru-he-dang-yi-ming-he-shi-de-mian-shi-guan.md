---
title: 谈谈如何当一名合适的面试官
date: 2017-01-09 22:19:25
tags: [工作方法, 招聘, 面试]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/36
---
### 缘起
今天项目负责人跟我一起面试了一个候选人，他说沟通能力不行，直接 pass，而我却不死心，因为没问清楚技术问题，于是直接叫他做个系统设计题，哎…… 直接给我转移话题，麻蛋。
但是，我也浪费了负责人的时间，要是上次我直接说这人沟通能力不行，也就没今天的事了。

另外，今天晚上，跟一同行交流『如何面试』的问题，恩，发现自己这块儿一直没有重视起来。

### 正文开始
人才的重要性不言而喻，面试是为公司项目选择合适的员工，作为一个面试官，你有权决定他能不能进入下个环节，因此，在短短一两个小时之内，搞清楚被面试者是不是符合需求至关重要。

先说说几个遇到过的问题：
1. 一个劲想考到被面试者，尽问些细枝末节的问题
> 没错，细节一定程度上可以衡量面试者的知识水平，但总是问细节的话，容易对他的认识有偏颇，毕竟还是有知识方面的『二八理论』。

2. 按个人喜好，肆意批判不同的技术工具、体系与文化
> 有自己喜好的文化，固然没有问题，但是以此来判断被面试者的能力，却只是显得狂妄自大而已。深以为，对不同的文化，我们需要有包容之心，你对不同文化的批判很大程度上是你没有经历过而已。同样的，对于企业来说，一个成功的企业文化绝对不会只是单个人的：求同存异才能使文化发展。

3. 不尊重面试者
> 不提前看简历，迟到，对被面试者的回答不满意时显得不耐烦（不耐烦是我犯过的错……）。毕竟被面试者千里迢迢赶过来面试，如果对方没有达到要求，那么请尽量告诉他有哪些地方不足，需要改进。这点在我面试小红书的时候很有体会，对方是个谷歌出来的工程师，当我回答不出来的时候，他会主动给我提示，甚至教我，告诉我哪些地方不足。（后来给我通过了，没去，但我到现在还是很感激他，他就是我学习的榜样）。

4. 问题不提前准备
> 有时候，你没有准备好问题，临时起意想问个问题，表述不清，让被面试者理解不了（当然，可能他也有理解上的问题）。然后你认为对方不会，他认为你莫名其妙。另外，提前准备的还有想清楚招聘什么样的人，以及对他的定位是怎样的。


总体来说，面试过程中，我们需要了解这几个点：
- ** 为人 **：『习武先习德』，这个很容易理解，每个公司都会考察，大部分时候是 HR 的责任，小公司里面可能需要你兼任，谈话的过程中，尝试了解他为什么求职，他说话语气、态度还有仪表，另外还有对工作的热情、毅力、自律与进取心等等；
- ** 知识 **：比如 js 中 prototype 是什么意思，Node.js 的异步是什么意思，mongodb 的索引是如何创建的，Linux 如何查看负责情况。一般来说，技术领域往往有很成型的技术栈，沿着技术栈挑几个从简单到难，由浅入深的一路问就行，记得有道题很有名：『在浏览器中，从输入网址到最终看到页面，说说在这个过程发生了什么？』；
- ** 经验 **：比如你做过的系统是如何设计的，如何优化的，有没有遇到什么问题，这是经验的体现所在，一般来说，可以从简历上的经验问起，然后拓展开来问知识，如果说不清，那就是有问题，或者经验欠缺，甚至是简历作假。另外，配合着还可以给做过的系统加需求，看看他如何应对；
- ** 能力 **：这个很关键，往往是我们最看重的，因为往往 ** 知识跟经验是能力的副产品 **：沟通能力，合作能力，领导能力，学习能力，解决问题的能力等等，而能力也是在短短一两个小时中比较难以衡量的；

### 如何考察能力
一般认为，直接让被面试者做题是简单而有效的，以考察服务器开发工程师为例：
- 算法 & 数据结构题：这是基础题，不用特别复杂的题目，比如写个字符串匹配；
- 系统设计题：算法之后的题目，这里考察被面试者的技术沟通能力、基础知识运用能力、以及系统分析能力，比如设计个订单系统、微博系统，从最简单的需求开始做；

这里最关键的地方在于：不需要被面试者给出完美的答案，而是他如何思考的，你可以提示他，甚至让他查资料，看看他设计的思路。算法题的话，看他如何考虑边界条件，是不是以及如何写单元测试，变量函数命名、代码风格等；系统题的话不断给他加需求，在一些关键点上给他提尖锐的问题，看他如何应对，思路是否清晰。

### 向被面试者学习
这点我想特别强调，** 把被面试者当成是你的同事 **，而且『三人行则必有我师』，你可以将工作中的问题包装成面试题，或者干脆让被面试者教你一些他擅长的东西，甚至让他说说他喜欢的一本书，你可能会在这个过程中重新认识对方。


### 最后
1. 再次强调请耐心，对被面试者的尊重，能反映你的修养与你所在公司的形象；
2. 犹豫就是不通过，每个进来的人必须高于目前团队的平均水平；
3. 面试后半段，尝试问他业余时间做些什么，也许他会成为你以后的朋友；

### Reference
- http://www.ruanyifeng.com/blog/2010/12/how_to_interview_a_programmer.html
- http://coolshell.cn/articles/4506.html
- http://coolshell.cn/articles/4490.html
- http://www.cocoachina.com/programmer/20160512/16225.html
- http://www.jianshu.com/p/e2b8b24ef58a
- https://zhuanlan.zhihu.com/p/20072954
- http://www.jianshu.com/p/e2b8b24ef58a

***
首发于 Github issues: https://github.com/xizhibei/blog/issues/36 ，欢迎 Star 以及 Watch

{% post_link footer %}
***