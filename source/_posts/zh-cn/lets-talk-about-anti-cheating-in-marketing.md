---
title: 谈谈营销福利的反作弊
date: 2018-02-10 16:05:48
tags: [业务,安全]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/70
---
<!-- en_title: lets-talk-about-anti-cheating-in-marketing -->

最近这些天，算是刷新我对于黑产的认知。

<!-- more -->

### 起因

事情起源于我们发现了客户端源码泄露，而对方利用我们平台漏洞而在刷我们的平台新用户福利，于是我们就去日志里面查找相关的记录，于是发现了短信接口里面的非常明显的爬虫：IP 固定，UserAgent 固定，请求频率非常高。

一般来说，在这个拉新成本越来越高的时期，每个平台为了吸引新用户，都会或多或少提供新用户专享的福利，或者进行一些营销活动促进用户活跃度，而这时候，就会被各种黑产盯上，因为这里存在套利空间，而如今这种黑产已经形成了一种产业链，你不得不防着点。

拿最明显的例子来说，用手机号注册刷新用户福利，我之前一直以为，手机号注册的成本很高，一个手机号，怎么也得几十块钱。

事实上，只能说我太年轻了，想得太简单了，如今拿手机号注册的成本很低很低，不妨去搜索下『验证码代收』（百度，淘宝，谷歌都可以），另外，物联网黑卡了解下？算了，知道你们懒，直接甩几个链接给你们看：

-   [灰产圈 | 互联网业务安全的黑灰产业链的故事](http://www.sohu.com/a/149730076_517801)
-   [黑产大数据：手机黑卡调查](http://www.freebuf.com/articles/others-articles/135317.html)
-   [双 11 黑产狂欢：数十万撸货大军，薅上一天，够吃一年](https://36kr.com/p/5102656.html)

想当年，O2O 上门服务红火的时候，被褥了多少羊毛，以及培养了多少褥羊毛的黑产，现在想想都后怕，假如当时知道这块内容，或许在[嘟嘟](https://github.com/xizhibei/blog/issues/59)的时候能防止我们的资金被褥走，能当一回拯救公司的英雄了。

接下来，我会详细说说这些天关于反『褥羊毛』的总结与思考，希望对你们将来做这块业务，或者已经做了这块业务却没有注意到黑产的朋友们有所启发。

### 反『褥羊毛』

其实这就是跟黑产博弈，魔高一尺，道高一丈。

#### 服务器层面：

1.  频率限制

这个很简单，我之前也在 [接口限流](https://github.com/xizhibei/blog/issues/29) 以及 [基于 ElasticSearch 的日志统计反爬虫策略](https://github.com/xizhibei/blog/issues/46) 中提到过，采用 IP 限制的方案，可以把单 IP 爬虫给禁掉。

另外，由于涉及到手机号，针对单个手机号也需要做限制，比如单个手机号每天最多允许发送 10 条，每条短信间隔不能低于 60 秒。

2.  机器学习

这个实现成本比较高，因为刚开始你需要进行有监督的学习，你需要不断去观察表现、调整输入、优化参数，另外，由于你没有相关的大数据积累，无法识别一些有明显问题的手机号。因此，建议使用大厂的一些服务成本会比较少一些，他们经过了这些年的积累，有大量的数据作为反作弊基础。

#### 客户端层面

仅仅从服务端还不能很好地解决这个问题，这时候就需要客户端去配合了。

1.  验证码

这是最简单而最有效的方式，加入相关的验证码就能把一大部分的爬虫给屏蔽掉，只是会有两个问题：一个是给普通用户增加了注册门槛，会影响拉新，另一个是有相关的『打码平台』可以以每个一分钱的成本打码，注意，是人工打码，不管你验证码怎么复杂，总是要人与识别的（对了，量大还有优惠），因此建议设置为拼图验证码，滑动验证码等，用户验证的体验会好些，成本比较低并且打码的成本高。

2.  接口加密

这也是常用的方案之一，只要跟客户端约定好相关的加密方式即可，然后客户端把代码加固即可，只是我不建议这么干：一是客户端不可信，不能排除客户端被破解或者干脆源码被泄露的可能性，再加上客户端发版本比较慢，一旦被破解，接口跟没有加密完全没区别；二是接口加密还增加了维护以及调试成本，毕竟是非明文的接口。

但是有一个例外，就是在 WEB 环境下，这时候可以利用 JS 加密，甚至可以每天都换一个加密算法，或者加入 token 验证方式，即通过特定的方式将 token 注入前端页面，前端请求的时候加上 token 即可。

3.  设备指纹采集

一般来说，真实用户是在真正的手机设备上使用的，而爬虫却不是，因此为了服务器配合使用的方式，即客户端在手机上采集一系列的设备信息，比如：

-   Mac 地址
-   移动终端设备码 IMEI
-   移动用户识别码 IMSI
-   AndroidId
-   IOS 的 IDFA 以及 IDFV

再加上用户行为数据

-   验证输入点击了几次，带上时间戳
-   登录过程用了多少时间

最后，当请求送达服务端，再采集一些 HTTP 层面的数据

-   User Agent
-   IP
-   X-Forwarded-For
-   Referrer

这些信息用来当做服务端的机器学习输入之后，能大大提高识别率。

#### 业务层面

其实这方面可以做的事情也很多，

1.  设备绑定

新用户使用了设备之后，相关的福利跟用户的设备绑定，一旦更换设备便不可以继续享受。这个需要业务开发的时候就注意这个问题，而可能开发成本比较高。另外，不能用户有多个设备同时使用的情况，需要跟业务部门协商。

2.  业务流程

比如用户先设置账户与密码，之后再验证手机号，而假如是金融相关的，就更简单了：实名加上资金验资。

### P.S. 1

在实践中，这些方案往往是结合使用，效率会高些，最重要的还是需要提高开发人员的安全意识。

### P.S. 2

这篇文章在帮助你认识作弊手段的同时，其实也是教你怎么去祸害各种平台，但是在我看来，技术从来没有对错，害人的还是人本身，利益在那里明摆着，就会吸引苍蝇。

作为技术人来说，我不会用这种手段去牟利，但是我不能否认他们的存在也是一种合理，在某种程度上甚至还有利于推动技术的发展，就如大自然中，没有绝对的光明，而我们的社会中也存在着犯罪，假如没有黑产，我们就会处于长久的温室当中，变得非常脆弱，一旦出现了『黑天鹅』事件，可能我们就会经受不住打击而迅速灭亡。换句话说，他们让我们更强大。

黑产的出现，也催生了一批帮助我们反作弊的公司，他们可以帮你以较低的成本解决这类问题，谷歌或者 BAT 等大厂就不用说了，这里介绍一个『验证码』相关的创业公司，在我见到过的很多安全验证平台中他目前是做的比较好的：[GeeTest](http://www.geetest.com/)（这不是广告，我这小小博客显然不值得投放 :P）。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/70 ，欢迎 Star 以及 Watch

{% post_link footer %}
***