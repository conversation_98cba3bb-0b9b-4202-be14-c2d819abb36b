---
title: 那些你可能在面试时会忽略的事
date: 2018-03-25 23:04:49
tags: [招聘,职场,面试]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/73
---
<!-- en_title: tips-about-the-interview -->

之前在 [谈谈如何当一名合适的面试官](https://github.com/xizhibei/blog/issues/36) 说了很多，但实际操作起来，会有很多的差别，现在把这几周的想法总结下。

最近接连面试了 30 多个候选人，然后只通过了三个人，这三个人有什么特点呢？

-   基础扎实
-   善于沟通
-   积极主动

现在就来说说，我为什么看重这三点：

### 基础能力

这个不用说，其实当年我也犯过这个错误：校招面试的时候，面对面试官各种苛刻的提问，很多都答不上来，其实这些提问其实都是学过的知识点，而我之前学的内容却只是为了应付考试，因而没有很好地掌握这些知识，于是连续被淘汰了好多次。我那时候也很郁闷：这些东西明明都是查课本或者上网一查都能找到的东西，为啥不看我在校期间做过的那些网页项目呢？

后来还是不得不在现实面前低头，重新复习了那些基础知识，然后通过了面试。

时至今日，我在这几年的成长过程中发现，正是这些基础知识，让我一次又一次解决了别人解决不了的问题，因为几乎所有的问题，就是基础知识的综合应用，即使遇到了一些我暂时解决不了的难题，我也会把相关的知识重新去看一遍，有时候就会找到思路。同时，也是基础知识让我一次又一次在一些项目上栽跟头：『搞了半天，原来这个 BUG 是因为这个啊！』于是又再去复习一遍。

其实，不妨把基础知识比作一座建筑的地基：这座建筑能建到什么高度，以及在这个世上屹立多久，几乎完全受地基的限制：地基约牢，建筑就可以越高，存世越久，否则就是岌岌可危，没准哪天都可能要倒掉。

所以，对于一个候选人来说，考虑到可能他还没有很丰富的实践经验，于是我们就会根据他掌握的基础知识来判断，这个人在以后是否有大概率变成拥有丰富经验的工程师。

另外再啰嗦一句：如果每次工作的时候，你都靠去上网翻书本解决，那么你每天将会有多久的时间投入到真正的产出里面呢？效率就是体现在这一点一滴的差别上面。

### 沟通能力

这个不用多说，沟通能力是职场中的必备技能，单兵作战能力固然重要，但在一个团队中，好的沟通能力能让你的其它能力真正发挥作用。

想想那些没完全沟通后就开干的需求，哪一个不让你之后边返工边泪流满面，最后哭晕在厕所；或者一个字段的理解出错，让之后整个项目延期，甚至出现线上重大故障（True story, T_T）。

沟通能力在整个面试过程中都能体现出来，但是为了能让候选人充分展示出来，有三处可以特别注意：

-   开始的自我介绍
-   中间的算法或者系统设计题
-   结束的自由提问

开头结尾就不用赘述了，而做题的时候，先撇开基础以及经验不谈，这也是最能体现沟通能力的地方：候选人思路的清晰度，对你提问的理解程度，以及这个过程中是否注重与你交流，**都可能在以后的工作过程中重现**。

### 态度

态度，其实就是说候选人有没有积极主动的意愿去做事，以及，让你感受到与这样的人一起共事，是否会很舒服，省心省事。

记得有个候选人，在我们提问了几个基础问题之后，针对答不出来的问题，统统回答：

> 『这有啥，上网一查就知道了。』

首先不说他基础知识的匮乏，单就这种傲慢的态度，就会让人不愿与他一起工作。毕竟通过面试的人可能就是要跟你一起工作的同事，或者下属，想必是没人愿意与这种让人讨厌的人一起工作的。

而目前这几个通过面试的那几个人，在面试时候，或多或少，都会在答不上来的时候，或者说一句『不好意思，这个知识点忘了』，或者有更棒的，会再问一句：『能不能教教我呢？』

别小瞧这句提问，这也许会让你觉得这时候提问挺无礼的，但事实却是，面试官会很高兴：一是你给了他『臭显摆』的机会，二是你表现出了好学以及主动的态度。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/73 ，欢迎 Star 以及 Watch

{% post_link footer %}
***