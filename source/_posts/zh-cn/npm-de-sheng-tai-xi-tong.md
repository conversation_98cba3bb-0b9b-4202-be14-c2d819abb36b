---
title: npm 的生态系统
date: 2016-06-12 23:55:51
tags: [Node.js]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/19
---
nodejs 这些年来的发展迅猛，一个重要的条件就是 npm，npm 这个包管理工具很强大，如果没有这个工具，很难想象没有 npm 的 nodejs 会发展到什么程度，golang 或许可以是个参照：发展到现在，一直没有一个统一并且非常规范的包管理工具。

我一直想很相信，任何东西的发展，离不开好的基础，无论是个人或是国家：

个人，需要多学习，多锻炼，提升自己，打好基础，比如语文，英语跟数学。可能有点好笑，但是，一个人的语文能力很大程度影响甚至决定他的语言能力，沟通表达能力，这些基础的能力在工作学习中的重要性不言而喻；英语的话，与外国先进的人或者事物交流的能力；数学，逻辑推理能力。所以，所有优秀的工程师都是有着非常扎实的基础。

国家，需要努力搞好经济建设，以此继续建设衣食住行，一些国家的基础设施，需要国家投入大量人力物力财力去建设，比如，铁路，电网，电信，公路等等。国家近些年来互联网的高速发展得益于基础设施的建设，智能手机就是一个很好的基础，以此形成的移动互联网，带来了多少机遇。这就是浪潮之巅啊。

最近刚开始看 KK 的失控，对系统的感悟越来越多，今天联系到 npm，随便说点，我认为，nodejs 的迅猛发展离不开它建立的生态系统，而 npm 是其中一个非常重要的基础，提供的基础核心价值『方便』，一条命令就可以安装所有依赖，并且开放整个系统，任何人都可以贡献安装包。

方便 + nodejs 官方 => 使用的人多 => 开源组件越来越多，质量越来越棒 => 更多的人以及组织参与，完全是个正向循环。

所以，如果以后要发明个语言，一定要提供官方的包管理器 😄 


***
原链接: https://github.com/xizhibei/blog/issues/19

![知识共享许可协议](https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png "署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）")

本文采用 [署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh) 进行许可。
