---
title: 像火箭科学家一样思考
date: 2021-05-26 22:23:31
tags: [读书,鉴书]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/172
---
<!-- en_title: think-like-a-rocket-scientist -->

这本书在我看来，就是在教你如何创业。因为，创业成功的难度不亚于一次火箭发射，虽然还会有更难的探月、探火星、载人，只是，这个过程中的方法论都是一致的，都是由普通的人类在用科学的方法，将不可能变为可能。

事实上，在技术行业，似乎做出一件伟大的产品似乎也是不可能的，比如你能在现在想象我们国家能在十年后造出可以跟 ASML 高端光刻机吗？

同样，可能你也无法在多年前想象我们国家有了自己的高铁、盾构机、003 航母、歼 20 战斗机，等等。

### 将不可能变为可能

这本书的作者是是奥赞 · 瓦罗尔（Ozan Varol），一个前火箭科学家，美国俄勒冈州路易拉克大学法学院最年轻的终身教授。 

整本书谈到的事情，其实就是如何将不可能变为可能。我们在工作中，肯定都会遇到当时认为不可能完成的事情，没有人会告诉你如何一步步去解决，如果这时候你能有书中告诉你的思维方式，你就完全有可能将不可能变为可能。首先就是绝不在一开始就承认不可能，起码需要去尝试，打破自己的固有认知，激发自己的创意，然后从一堆的创意材料中不断尝试、失败与总结，最终找出那个能点亮灯泡的钨丝。

对于创业的人来说，这种思维难能可贵，因为在他人看起来困难重重的事情，在你眼里却是充满机遇，你可以用火箭科学家的思维方式来打破不可能，重新定义现状，开辟新道路，打出一片江山来。

> 科学 “不仅仅是知识，更是一种思维方式”。 

其实在小时候，老师问我们想成为什么的时候，好多的同学说要成为科学家。我小时候也是这么想的，只不过我认为的科学家可能就是火箭科学家了，因为那时候刚从书本上知道了我们国家在 1970 年发射过东方红一号卫星。等我长大了，没有成为火箭科学家，更没有成为科学家，成为了一名计算机工程师（好吧，就是程序员）。

不过你看，其实这两者有相同之处：我们都在用科学的手段解决问题、实现梦想。

### 自己的一些感悟与总结

接下来谈谈我从书中学到后，自己的几点感悟，涉及大量剧透。

#### 拥抱不确定性

我们的本能让我们对确定性非常迷恋，因为这是自然选择的结果：进化心理学也告诉我们那些追求不确定的人在远古时间往往难以生存，所以他们的基因无法传递给下一代。

但是当我们能克服这种倾向，敢于冒险，机遇才会向你招手。

> 只有当我们敢于牺牲确定性答案，敢于冒险，敢于远离路灯的时候，才能真正实现突破。

美国的阿波罗登月计划，是在没有完全的准备下才开始，事实上：

> 肯尼迪发表演讲之时，与登月相关的许多技术标准甚至还没有制定出来，美国宇航员从未在宇宙飞船外工作过，宇宙飞船也从未在太空中进行过对接。

这幅场景，简直就跟老板说下周要看到某个巨复杂的功能一样，大家刚刚听到后，估计也是一脸懵，同样会跟 NASA 那帮科学家一样束手无策。但话说回来，如果你能担当起这个责任，拿出方案来告诉老板为什么不可以做，或者如何去实现、并且做成做好了，那么你解决问题的能力就会越来越高，当然，同样越来越高的还会有你的位置与薪资待遇。

> 只有当我们注意到一些微妙的线索时——数据有些问题，结论下得太快或流于表面，观察结果并不完全符合理论——旧模式才能给新模式让路。

前面说了那么多，但我们没法绕开的就是风险了，因为冒险就意味着有着很大的风险。这时候，我们就需要采取**冗余和安全边际**着两个工具了。

其实这就是所谓的高可用了，当你把程序部署到机器上去，它所面临的真实情况很可能就是你的测试用例所无法覆盖的，这时候就要用 Plan B 之类的冗余方式来保证高可用，不至于让程序再也无法启动，同时，安全边际就意味着你的服务能够承受较大的破坏，一个例子就是即使你 80% 的服务器都坏了，你依然能够用剩下 20% 的服务器来保证核心功能的正常使用。

另外，巴菲特与查理恐怕是最懂这个道理的人了，因为在他们价值投资的理念中，最重要的一条就是保证自己的投资标的安全边际足够大，或者说足够便宜，来保证他们的投资即使遇到股灾也不会损害到太多本金。

#### 第一性原理

这句话被硅谷钢铁侠说出来后，一夜火遍大江南北，全球的科技圈都知道这句话了，其实它说的内容很简单，也就是回归本源。

> 每次革命性创新背后的要素原创性在于回归本源。

敢于质疑现在的每一个不合理之处，回归本源去思考来龙去脉。

作为一个软件工程师，当你遇到不合理的需求的时候，你会反驳吗？其实这种批判与质疑一切的思维方式，就是第一性原理：你完全可以在接到需求的那一刻，开始思考这种需求背后的需求，尽一切可能去了解真正的用户需求，从而能给出真正合理的解决方案，而不是在用户三天两变的口头需求下疲于应付。

用户不会知道他们想要 iPhone 这样的手机，只会在乔布斯发布后才会真正知道想要的是这样的手机。与此同时，诺基亚这样的传统手机厂商只会一次又一次将他们的功能机一次又一次优化，从来不会去质疑需求的合理性。

突破常规思维才能创新，努力跳出路径依赖：「别人就是这么做的」，这句话怕是当你提出问题时，太多的产品经理以及老板都会说的一句敷衍的话语。长久以来互联网领域的复制是如此简单而有效，导致大家互相抄袭，而且但凡有个创新的地方，没过几天就会被竞争对手抄袭，于是大家都逐渐忘记了当初为何那么做。

> 我们总以为同行和竞争对手知道的比我们多，我们往往喜欢复制粘贴他们的做法，尤其是在形势不明朗的情况下。
>
> 知识确实是个好东西，但知识的作用应该是给人们提供信息，而不是起约束作用；知识应该启发智慧，而不是蒙蔽心智。只有让现有的知识不断进化，我们的未来才能变得越发清晰。
>
> 不知不觉中，知识可能会让我们成为惯性的奴隶，而惯性思维只会产生常规结果。

其实，第一性原理背后，是变化的环境，当初看起来合理的方案时过境迁，我们需要回溯到过去，找到那个最初的问题，重新去思考，不要被过往的知识束缚。

#### 「即飞即测」原则

这点其实告诉我们的是，在我们给出解决方案后，要及时在仿真环境中去测试。这个原则放到我们的软件工程里面，其实对应的就是各种各样的测试：单元测试、集成测试、e2e 测试、压力测试等等。但是，真正重头戏的还是放到线上预发布环境后的测试，因为那里是一个仿真环境，那里有与生产环境中非常类似甚至一致的用户测试数据，是我们放到生产环境前的最后一道关卡。

> 在正确的测试中，你的目标不是发现所有可以顺利进行下去的东西，而是发现一切有可能出错的东西，并找到极限点。

是否重视测试，是作为一个区分一个软件工程师是不是真正靠谱的标志，因为没有人能保证自己的东西一定不会出错，但是我们却可以通过各种测试方法来减少出错的可能。

> 倘若不进行系统测试，就可能产生无法预测的后果。产品出厂前的最后一刻，如果你要对产品做修改，却不重新测试整个产品，那你就要冒灾难性的风险

#### 失败与成功相生相伴

对于失败，我们有太多太多的讨论，无论说失败是成功之母，还是失败是失败之父等等，都会有他们各自的理由。但是这本书作者认为的，要认为成功与失败这两者的界限很模糊，或者说同等重要。

> 每次失败都是一次宝贵的学习机会，每次失败都会暴露一个需要修正的缺陷，每次失败之后，我们都会朝着最终目标迈进一步。

类似的观点我在 [如何理性地失败：黑匣子思维](https://github.com/xizhibei/blog/issues/106) 讨论过。只是在最后我说过：

> 建立容忍失败的文化，以及事后分析的制度。

但是却无法提供可以落地的实践，但是本书也介绍了两个有意思的实践：

一个是由领导带头，将失败经历公之于众：

> 一项研究还表明，人们仰仗领导者开启变革。如果领导者不承认自己的过失，如果有人认为领导者从不犯错，那么，我们就无法指望员工会冒险质疑领导者或者揭露他们自身的错误。

另一个是学会体面地失败，即所谓的「暴露疗法」，经常让自己暴露在失败面前，学会习惯性的失败，这就是所谓的**平时多流血，战时不流血**。

### 最后

相信大家看出来了，在书中提到的很多方法，不就是软件工程领域的一些方法吗？是的，简单的说法是科学的方法都是是相通的。

不过，我还是忍不住多说一些。肯尼迪在宣布登月计划后的解释说：「因为我们现在不知道前方有什么好处等着我们」。这项计划最后的结果就是，登月成功，花费巨大，但是却极大提升了国家整体工业水平，催生出一系列先进技术，其中当然就包含给计算机工程启发的各种思维方式。

我们能从航空航天里面获得太多东西，也能学到太多东西，这也正是我们国家在发射东方红卫星时，即使还处于贫困阶段，也要这样做的原因，因为这是一项功在当代，利在千秋的事情。

### Ref

1.  [中国为什么要有人造卫星，答案也许就在五十多年前的这封信中](http://www.kepu.net.cn/ydrhcz/ydrhcz_zpzs/ydrh_2020/202004/t20200423_485122.html)
2.  [NASA 科学副总监 1970 年回信修女为何要探索宇宙](http://discovery.163.com/12/0813/10/88PIELRG000125LI.html)


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/172 ，欢迎 Star 以及 Watch

{% post_link footer %}
***