---
title: 2020 总结
date: 2021-01-01 21:40:44
tags: [年度总结]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/157
---
<!-- en_title: summary-of-2020 -->

今年总的来说，是比较**颓**的一年，首先是疫情带来的影响，无论是成长还是机遇；其次是自己今年放弃了几个好习惯，比如坚持每两周一篇的博客，每周两次的锻炼以及坚持阅读。

对于新冠疫情，到目前还是没有结束，虽然已经有疫苗上市了，看情况还是得持续一段时间。不过也有好事，各种公共场合大家都很自觉地戴着口罩，于是除了新冠病毒，今年各种呼吸道传染病减少了很多。虽说没有染上新冠，但是自己身体上的退步还是很明显的：今年又确诊了两种慢性病，腰肌劳损以及慢性胃炎。

### 厨艺

去年给今年定的计划中，有提高厨艺这个选项，然而拜疫情所赐，这个计划竟然是第一个完成的，并且提前完成了大半年。

目前最喜欢做，也是自己最喜欢吃的菜：

-   日式肥牛饭
-   鸡肉咖喱饭
-   腊肠焖饭
-   意大利番茄肉酱 + 意大利面
-   螺蛳粉（这个也不算菜，只是今年吃了好多 :P）

### 博客

今年明显感觉到了写博客的吃力起来了，写了一个 CMake 系列之后，就很难有比较好的内容来进行写作了，我觉得，这也是自己积累不够多的表现。

尽管也想去写其它的系列，比如密码学、数字图像处理，奈何自己投入的精力不够，也可以说是积累不够，没法一下子就把这几块骨头给啃下来。

不管怎样，这个系列是自己写的最长的，也算小有成就感：

1.  [（一）入门](https://github.com/xizhibei/blog/issues/133)
2.  [（二）第三方依赖管理](https://github.com/xizhibei/blog/issues/134)
3.  [（三）ExternalProject 实践](https://github.com/xizhibei/blog/issues/135)
4.  [（四）用 GoogleTest 测试](https://github.com/xizhibei/blog/issues/136)
5.  [（五）安装、打包与导出](https://github.com/xizhibei/blog/issues/137)
6.  [（六）用 Doxygen、Sphinx 与 Breathe 创建文档](https://github.com/xizhibei/blog/issues/139)
7.  [（七）常用变量、函数以及模块](https://github.com/xizhibei/blog/issues/140)
8.  [（八）交叉编译](https://github.com/xizhibei/blog/issues/141)
9.  [（九）实战之如何下载依赖](https://github.com/xizhibei/blog/issues/142)
10. [（十）编译速度以及程序性能优化相关](https://github.com/xizhibei/blog/issues/145)

这方面的总结可以看 [CMake 系列完结以及一些碎碎念](https://github.com/xizhibei/blog/issues/148)  ，就不在这里赘述了。

### 技术

现在主业又开始了新的技能树分支，逐渐拓展自己在 C++ 以及嵌入式系统方面的知识，今年最大的体会就是，原来硬件带来的性能提升是那么明显（当然了，可能是因为自己不怎么玩游戏，没体会过更换显卡带来的性能体验）。

就拿加密算法来说，在普通 PC 上实现的 AES 256 ECB 算法，可能会达到 80M/s，而到了嵌入式设备上，纯用 CPU 就会变成 5 M/s，而这时候换成硬件加密，则能够达到 100M/s 。

另外就是 C/C++ 了，虽然是之前学过，但是如今再次上起手来还是会觉得不适应，毕竟接触过更现代的 Go，Rust，Node.js 后，就会有天然的不适应。缺少了那些随手可得的工具、依赖以及文档，实现功能时需要考虑的东西更多了，尤其当涉及到操作系统、内存管理的时候，非常让人头大。

所以，目前的开发过程中，会尽量不去考虑引入新的依赖，如果要引入依赖，首先是查找一番，对比几个项目的优劣，然后，必须要读源码，尤其是那些文档不佳的项目，还必须深入阅读。如此我才明白有经验的 C++ 工程师是多么宝贵的存在了，凭借熟悉的那些依赖，他就可以在更短的时间里面把事情做成，并且做好。

### 读书

读书有点遗憾，今年把过多时间用来刷 B 站了，虽然不会跟抖音一样令人沉迷，但是一旦看了之后还是会在不知不觉中消耗了时间，好吧，其实也可以算是沉迷（比如非常喜欢观察者的一些节目，比如王骁的骁话一下、马前卒的睡前消息、董佳宁的懂点儿啥以及沈逸老师的逸语道破，当然还有罗翔老师的刑法学等等），只是在这个过程中，也会学到一些知识，甚至我觉得对比与书来说，吸收知识的效率更高。

话说回来，这些知识，也是所谓的碎片化知识，无法带给我系统的知识，所以理性点说，就是娱乐。

### 理财

首先报告下今年的理财成绩：25%，只是粗略计算，实际应该会比这个数字高 1%-2%。

之所以达到这个程度，我认为一个是自己看准时机下重注了：疫情初期自己也几乎是拿出每月的工资去抄底基金了，然而最重要的是国运发展好，最终控制住了疫情，让我敢于抄底。

在证券市场中，我们把贝塔收益指市场平收益，而把超出市场平均回报的收益叫做阿尔法收益，所以大家明白了，今年我的主要受益来自于**贝塔收益**。

### 记账

今年记账了一整年，几乎手动记录了自己所有的支出，也几乎养成了每笔消费之后的习惯性记账。

有人可能会说，现在有那么多自动记账的工具了，为什么还要自己费劲手动记账呢？确实现在几乎每一笔消费，都会被微信、支付宝甚至银行自动记录下来，并且还能够提供一些简单的分析、总结。只是，它们共同的缺点就是很难去兼容彼此，一旦你用不同的银行或者客户端，都有可能将你的消费打散开来。

还有人会说，写个脚本统一导入记账软件即可，这个我不否认，有些记账软件可以直接对接支付宝微信的导出账单，甚至银行。非常便利，而我不选择的理由就是隐私，作为互联网从业人员，我深感个人隐私泄露的可能性之高，因为数据库被拖，甚至主动卖出数据也是见怪不怪了，因此，我对于国内的这帮互联网大厂，有深深的不信任感。不过，我相信在国家力量的介入下，法制会越来越完善，行业也会越来越注重用户的隐私，真正把用户的隐私当回事，而不是永远以为用户会用隐私换取便利。

其实，之前尝试过不同的记账方式，到了最后都逐渐放弃了，因为那些记账方式，都没有让自己深度参与到其中，记账的工作被机器自动化了，以至于对于最后的账单统计也最多只会轻叹一声：「天，这个月花怎么花那么多？」。

说了半天，其实自己手动记账是一个**能让自己对于花出去的每一分钱更有意识的行为**，这样做，多少都会找回那种数字简单减少而产生的无动于衷的感觉。

今年记了一整年的帐后，从月账单的统计中，真正意识到了自己在过怎么样的一种生活。然而，自己还是缺乏制作并实施预算的习惯，所以，也没法去掌控自己过什么样的生活。

### 最后

新年快乐，博客我还会写下去的，毕竟还有那么多的朋友关注我，只是目前频率不能保持恒定了。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/157 ，欢迎 Star 以及 Watch

{% post_link footer %}
***