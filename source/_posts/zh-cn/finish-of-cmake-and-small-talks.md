---
title: CMake 系列完结以及一些碎碎念
date: 2020-08-30 16:01:56
tags: [总结]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/148
---
<!-- en_title: finish-of-cmake-and-small-talks -->

这过去的几个月中，我一直在写 CMake 相关的内容，写成了一个系列，到今天，我打算先放下了。

事实是，我似乎是陷入灵感枯竭期了。

### CMake 系列的总结

先来做个总结，CMake 系列，一共写了十篇，从基础，到高级内容，基本上都是我在工作中的学习内容，没有很精深，也没有写的很全面，毕竟自己重新写 C++ 也不过这过去不到一年的时间。

回顾 CMake 系列，我发现写一个系列的话，心理压力会小很多，毕竟题目就限定在一个比较小的范围之内了，内容自然就比较容易写。

另外就是 CMake 本身也不难，资料以及社区的内容都比较丰富，基本上遇到的问题都能够通过搜索来解决。

但是，这也带来了问题，因为光顾着写 CMake，没有注意到其它内容的缺失，导致了选题的懒惰，没有去挖掘其它可以写的内容，等 CMake 写到了后期，这种感觉就会非常强烈：要结束了，我该写点什么东西呢？

再回顾这几年来的博客，发现这些博客基本上就是自己的工作总结了，基本上学到了什么，就会在博客中写什么，所以，看起来，我这过去的几年，学的东西很杂。

好了，到这里，我认为 CMake 本身算是自己在写长篇内容方面的一次尝试，就这个尝试本身而言，算是自己的成功。

### 为什么灵感枯竭

这次的博客，整整迟到了一周，至于灵感枯竭，我分析了下原因。

#### 完美心理

把很久之前写过的文章拿出来，自己看了就会吐槽，写得真烂，但是，当初自己写的时候，并没有那么多的顾虑，篇幅写的小，内容也不多，但写的过程却很轻松，经常是打开网页就能写了。

现在，心理完全变了，因为偶尔会写出自己都觉得不错的文章，于是，对自己的要求也会变高，那些很水的文章，自己不屑于去写了。

于是，问题就来了，如果看过我大部分文章，就会发现，目前的更新频率没有变，但是更新的日期却是经常拖了，从前是周六，周日，现在一拖再拖，变成周三了。

问题是什么呢？完美心理，或者说是拖延心理。

其实，写自己不屑于去写的东西，并且把内容写好了，写得通俗易懂，照样会是好文章，哪怕是一篇手把手教着小白编程的文章，也会有它的价值所在。

#### 输入不够

最近几个月，学习的内容，也变少了，比如技术上就局限在嵌入式、摄像头 ISP 上，一方面是自己不熟悉的内容，另一方面，却是一直在炒冷饭。

读书也变少了，严格来说，是技术内容变少了，最近看的几本都是金融类、以及小说。

所以，古人说「读书破万卷，下笔如有神」不是没有道理的。

因此，书不能停。

### 接下来写的预告

之所会有这个，大概我想通过预告来逼着自己多些点内容，以及看看大家有没有感兴趣的，可以收集点意见。

目前，我大概有这么几个可以写的：

1.  技术内容
    1.  密码学的基础以及实践
    2.  数字图像的处理
    3.  Golang 源码的解读
    4.  Linux 工具：iptables、ip、rsync、sed、awk、bash 等
2.  非技术内容
    1.  投资理财：股票基金以及债券基金
    2.  工作效率：GTD 以及 番茄钟

另外，CMake 其实还有内容可写，如果有机会的话：

1.  CMake 的样例解读 OpenCV
2.  CMake 偏小方面的知识，如 RPATH、Generator expressions、Policy、Qt


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/148 ，欢迎 Star 以及 Watch

{% post_link footer %}
***