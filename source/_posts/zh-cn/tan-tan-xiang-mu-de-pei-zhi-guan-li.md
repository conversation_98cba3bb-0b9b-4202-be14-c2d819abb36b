---
title: 谈谈项目的配置管理
date: 2016-09-11 21:28:22
tags: []
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/27
---
众所周知，配置在项目开发中非常重要，一般来说，我们都会根据环境会有不同的配置，比如本地开发会配置本地机器的数据库，测试时还会给数据库设置不同的本地数据库，然后线上线下也会有不同的配置，其中非常值得注意的便是：生产环境的配置，因为这些配置非常敏感，需要加密。

把配置直接放进代码管理 repo 是最低级的做法：历史记录会有永远记录着配置中的敏感信息，看过不少初学者这么干，一搜历史记录所有的密码都出现了，github 一搜一大堆，更有甚着，把密码替换掉，然后 commit。。。哎。。。

如果这个项目只是公司内部，不会被外人发现，那也倒勉强可以接受，只是一旦公司大了，这些敏感信息也不是所有开发人员可以看到的，于是就很麻烦了。

那么，接下来说说，怎么解决：

我们的需求：** 安全 & 方便 **
#### 剥离

一般来说，最简单的方式：从项目文件中剥离，并 ignore，git 中是放入. gitignore。然后配置单独管理，只有被授权的开发人员才能获取查看。只是这样的话，单独管理增加成本，前期几乎也是对所有人可见，新开发人员需要获取开发配置后才能继续，每次修改生产环境配置的话，都得一个个修改配置文件，以及对 CI 以及 CD 不友好（一般来说，CI 还有 CD 都是直接清理本地非 repo 文件，然后拉取新代码，于是你得添加一个专门获取配置的过程）。
#### 加密配置

比如 [Blackbox](https://github.com/StackExchange/blackbox)，是将配置文件加入 repo，但是呢，是加密过的，利用 GPG 加密，安全性还是不错的，也很方便管理，只是，会有一些学习成本，在接受范围之内。
#### 单独管理动态

动态以及非初始化配置，可以放到专门的配置管理服务器上，比如 [Vault](https://www.vaultproject.io/)、zookeeper、etcd 还有 consul，其中 vault 非常适合敏感信息的配置，它甚至可以做到即使黑客侵入内网，也不会像静态文件那么容易获取信息。还有个非常棒的优点：统一管理，不用担心每个服务器上的配置不统一，也能动态的更新。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/27 ，欢迎 Star 以及 Watch

{% post_link footer %}
***