---
title: 2019年总结：柔软的三十岁
date: 2019-12-30 19:52:01
tags: [年度总结]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/128
---
<!-- en_title: summary-of-2019-the-soft-30 -->

![x<PERSON><PERSON><PERSON>](https://blog.xizhibei.me/media/15701774378793/xiaohua.jpg)

为什么说三十是柔软的呢？

柔软这个词，大概是我与小花（图中的猫）的关系开始缓和的时候开始意识到的，之前会一直挑逗它，摸它的肚子与屁股，所以总是被挠被咬，然后我就打它，所以它跟我关系一直不是很好。直到某一天，我开始只摸它的头与下巴，然后给它买吃的，我们俩关系就变得好多了。

<!-- more -->

### 前言

> 子曰:“吾十有五，而志于学，三十而立，四十而不惑，五十而知天命，六十而耳顺，七十而从心所欲，不逾矩。”  ---《论语 • 第二章 • 为政篇》。

孔子说的『立』指的是他知礼、言行得当，而如今社会一般认为，立代表的是三个立：**立家，立业，立身**。<sup>[1]</sup>

在当今这个社会，立家与立业乎不太实际，尤其是对于在大城市打拼的年轻人而言，因为现在这个阶段，能凭自己的能力达到在大城市立足，很难。

比如那直击灵魂的三个问问题：房子首付够了？有车了没？结婚生孩子了么？

这么问，可能会显得很世俗，但事实确实如此。不过，对于我们这代人来说，三十而立的意义更多的在于精神上的，即**立身**。

今年的总结，就不按之前的套路来了，因为那样的话，显得比较像流水账，分享的价值也不多，于是这次按自己的收获，划分为几个适合与大家分享的内容：

-   严于律己，宽以待人
-   放低姿态，耐心做事
-   重视沟通，控制情绪
-   认清事实，时常乐观

### 严于律己，宽以待人

2019 年，我还在坚持那几件事：

-   坚持写博客：目前还没有断过，今年年中开始，写作时间逐渐变成了周一晚上，算是一项改变。不得不承认的是，今年的质量有所下降，这在我看来是自身的提高不够，输入不够，输出自然也就不够。
-   检查学习：今年读书的数目就不提了，一是数目本身没有意义，二是有些书我觉得一遍不算读完（我会在最后列举值得一读的书）。
-   坚持投资：从去年寒冬到现在一直是定投，到现在来看，去年的损失今年全部补回来了，而且还有浮盈。
-   坚持运动：今年上半年有几次跑步用力过猛，得了髂胫束综合症，然后一直到现在都是恢复为主，主要运动改成了游泳。

这里特别提下游泳，其实一直是自己自学的，相关要去参加培训班，但似乎总没凑出时间，所以还是按照自己想法来，找了一些视频自己学习。

目前达到的成就是：

1.  一小时内游泳 1650 米，50 米泳道，平均配速 1 分 51 秒；
2.  初步学会了水中翻滚转身、蝶泳；
3.  自由泳最高一次 850 米；

相对于自己的以往，在游泳方面的进步是非常明显了。

尽管这些方面比较律己，我也有不律己的地方：早睡早起（说起来都是泪）、锻炼不规律。

圣贤经常教导我们『严于律己，宽以待人』，我觉着，这大概是因为世人大多都是反着来的：对别人很严格，对自己很宽松。

我也是，当他人出现问题的时候，会帮他们分析，应该如何去改善、去解决，但是这种方案往往要求比较高，比如坚持早睡早起、运动、学习、理财，而同样的情况到我身上的时候，就不会那么严格要求自己了，还会在心里想着：**你不是我，怎么会懂得我的苦恼**。可能还是给别人建议不花钱，而自己实施却要费心费力，可以『耍流氓』。

我觉得本质的原因在于立场，我在 [谈谈 996.ICU：原来我们可以这样抗争](https://github.com/xizhibei/blog/issues/103) 说的，也基本上是这个问题了，我们可以看到，立场切换之后，我们可以完全站在了原本自己的对立面。

所以，现在说的严于律己，其实是不试图让不同立场的人完全理解自己的立场，更不要以自己的立场伤害到别人，而要尝试着站在他人的立场上去思考；而宽以待人，说的是承认他人有自己的立场，更有不同于自己的认知体系，我们需要尝试以更宽容与开放的态度去沟通。

### 放低姿态，耐心做事

以前那些认为没有什么意义的事情，现在会觉得更有意义了。

换了个房子租，房子依然会有问题，但是现在能够自己想办法去解决这些问题了。

比如去年刚来深圳的时候，选择与别人合租，租金似乎还比上海高一点，并且有蟑螂，外面修地铁、造楼的声音吵，我还是挺佩服自己的忍耐能力的，忍了差不多半年，突然有一天想通了，或者应该说是，终于认清了这个事实：只有我自己才能对自己负责。

等待别人帮自己搞定，是一种高姿态，而现实会把你的脸摁在地上摩擦。

与其等待房东或者朋友帮我解决，还不如自己上手，稍稍发挥自己的资料搜集能力，就知道如何解决了：蟑螂有蟑螂药，还需要定期打扫屋内的卫生，而噪音的话，有耳塞还有窗户密封条。

又比如年初让我来装机器的操作系统，我会以用不到我来装系统来推脱，毕竟我还有『更多更重要的』事情去做，但是到现在，我不仅会去做，还会尽力把它做好：[如何用 PXE 在一小时内安装完 10 台以上的操作系统](https://github.com/xizhibei/blog/issues/123)。

这些『不重要事情』的意义在于哪里？对自身来说：打破舒适区，提升认知，锻炼耐性；对于一个团队来说：确保每件事，无论大小都有人去落实，最终的成功离不开这些细节的累积。

在之前，一直认为 Node.js 才是自己的终身所需，现在回想过来看还是太过于封闭，于是积极转型 Golang，然后又简单学习了 Rust，目前由于接触了机器学习框架与嵌入式，重新捡回了 C++ [为何 C++ 静态链接库顺序很重要](https://github.com/xizhibei/blog/issues/100)。

说好听点，这是跳出自己的舒适区，而对于我来说，就只是喜欢折腾而已。

说到这，这里应该有今年 Golang 的文章列表：

-   [Golang 中的测试](https://github.com/xizhibei/blog/issues/95) 
-   [Golang 中的跨语言调用](https://github.com/xizhibei/blog/issues/98) 
-   [权限引擎之 casbin](https://github.com/xizhibei/blog/issues/102) 
-   [MongoDB 的复制集](https://github.com/xizhibei/blog/issues/105) 
-   [如何优雅重加载 Web 应用](https://github.com/xizhibei/blog/issues/108) 
-   [Golang validator 详解](https://github.com/xizhibei/blog/issues/110) 
-   [Golang 中的异步任务队列](https://github.com/xizhibei/blog/issues/115) 
-   [Golang 中饱受争议的 context](https://github.com/xizhibei/blog/issues/118) 
-   [如何实现一个安全的 SaaS/PaaS 服务](https://github.com/xizhibei/blog/issues/121) 

### 重视沟通，控制情绪

这里要特别推荐一本书：《关键沟通》。

这里的**关键指的就是拥有不同观点之间充满风险与情绪的关键时刻**，这样的时刻在我们工作生活中会很多次出现。这样的时刻，能处理好，那么我们接下来与他人相处与合作会很愉快，作为一个团队的话，能发挥很高的战斗力，但是处理不好，则会让我们与他人关系造成裂痕，甚至转为敌对关系，增加团队内耗。

处理的原则，**一是明确自己的目标，即想通过沟通达到什么目的，二是注意维护对方的安全感，让对方毫无压力地与自己沟通**。

比如我们有时会与周围的人进行辩论，辩论到最后就为了辩论而辩论，开始吵架，甚至开始人身攻击，而到最后大家事后反思，就会觉得自己好羞愧、好傻，大家吵来吵去的甚至只是同一个观点的不同描述。

其中，我觉得最关键地方在于情绪。

很少有生来就能控制自己情绪的人，其实关键还是在于能意识到自己的情绪，其后才是学会控制。

情绪来自于自身对于事实的反应，同样一件事件，不同的人会有不同的反应，而这不同的反应很大程度上只是认知的差别而已。意识到这一点之后，你就需要控制情绪，然后改变自己的沟通方式，比如**少说观点与结论，多说事实与现象**。

先从意识到自己有情绪开始，学会意识到自己的情绪，可以从阅读新闻、自媒体文章开始，如今很多自媒体的传播很大程度上是依赖于情绪的传染，如今看了越来越多的文章后，就自然会觉得需要警惕这种文章，这些文章会不断刺激你的情绪，从而达到影响你情绪的目的。这种时候，就需要意识到，自己看文章时，是不是感到了焦虑、生气、不忿等等，如果意识到了自己的情绪在变动，就会明白这些文章的目的何在了。

慢慢地，等自己能控制情绪后，就需要学会更好一点的沟通方式。

下面举个产品经理与工程师的对话：

-   『我觉得你很不靠谱，做的页面好差劲！』
-   『你才不靠谱呢！』

两人开始了撕逼。

-   『A，你上周完成我交给你的页面后，没有跟我打招呼就下班走人了，后来我测试的时候发现了几个页面错误，比如按钮点不动，数据也看不到，这让我觉得你不靠谱，所以是因为你是页面没完成吗？还是说你还有其它更重要的事情要做？』
-   一阵脸红，『哦，不好意思，我做好的页面没有部署到测试服务器，我现在就帮你部署一下。』

两人继续和谐而又愉快的对话。

同样一件事情，由于产品经理不同的沟通方式，造成了完全不同的情形，原因就在于后者让对方明白自己是因为什么事实而产生了想法与观点，并且也没有采用先入为主的说法给对方打上『不靠谱』的标签，而是**就事论事**。

另外，对于情绪的控制，还需要知道如何改善**自卑**。

自卑有自卑感与自卑情结两种说法，两者的区别就在于，自卑感是正面的，它可以通过自己的努力来改善，甚至它本身就是一种催人上进的力量，正如你会觉得『我的编码水平还有待提高』；而自卑情结是负面的，往往拥有它的人会觉得自己不如别人，并且会一直不如别人，『我身边大牛这么多，这辈子我都不可能成为大牛了』。

> 健全的自卑感不是来自与别人的比较，而是来自与 “理想的自己” 的比较。

其实这些是阿德勒在《被讨厌的勇气》中的内容，他还提到：自卑感来自主观的臆造。

另外，阿德勒的《自卑与超越》也是同样推荐一读。

### 认清事实，时常乐观

关于『事实』本身，物理学的发展史，给我留下了深刻的印象，曹天元在《上帝掷筛子吗》历数了量子物理发展的过程，当初发现物理学界的那帮大牛们也会对新的事实采取不认同的态度。

比如爱因斯坦的相对论，尽管一时让他人无法接受，最终却让牛顿的经典力学退出了历史舞台，但是另一方面：他直到去世的时候，波尔都无法让他相信量子论的解释是正确而完备的。

> 科学是什么？很多人认为，科学无非就是在不断接受新信息的同时，调整一个命题成立概率的过程。

那为什么还有那么多人无法认清事实？其实就在于人会有**认知失调**，死不承认自己的错误，就如我在 [如何理性地失败：黑匣子思维](https://github.com/xizhibei/blog/issues/106) 中提到的，承认错误才有利于进步。

对于我而言，我开始慢慢接受自己在各方面的不足了，比如我的博客目前还是很一般，在 GitHub 的这个平台上，还没法做到与更多的人交流。因此我在考虑拓展其它平台了，当然这也需要多花费些精力了。

在认清事实后，我们要做的不是自卑、顾影自怜甚至悲观，我们当然需要保持乐观。

这里想送给所有人一句话：**悲观的人最终会收获正确，但是只有乐观的人才能收获财富**。这句话改编于之前网上很流行的一句话（出处不祥了，有说法是美国的谚语）：

> 悲观者往往正确，但乐观者往往成功。

一下子可能难以理解，我可以说个买股票的例子：

悲观的人永远在持币观望，而乐观的才不管处于什么时候，都会投身于于股市。于是我们可以看到，悲观的人在下跌的时候不敢买入，而上涨的时候也会觉得总会下跌的，还是不敢买入，于是他到最后一直都是『正确』的，但由于他一直没有买入，他永远也不会收获财富；但是乐观的人却不一样了，敢于在任何时候都处于市场之中，于是他可以在下跌的时候调高仓位，上涨的时候调低仓位，他可能会在这个过程中暂时损失一些，但长远来看，他总是能在最后收获财富。

再说个创业的例子：

在这个万众创业的社会中，大多数人都会在这个浪潮中被洗刷下来，也就是创业失败。只是，回顾我国改革开放以来，真正收获巨大财富的人，都是敢于在当初真正把自己投身于创业浪潮中的人，他们就是乐观的人。悲观的人永远在担心会失败，所以异常谨慎，不敢『下赌注』，因此还是像上面一样，他收获了正确，但是他无法收获财富。

可能你会说，按照事实来看，大多数人在现实中还是无法成功的，在这个前提下，失败概率那么高，我还要『入场』吗？这样的说法确实没有问题，因为我的说法是乐观面对未来，这需要承担风险，如果没有面对风险的觉悟，那确实不适合入场，能接受保本的低收益也未尝不可。

对于无法认清事实的人，可以『引诱』他进入股市，或者去创业，事实会让他学会重新做人。

### 最后的总结

我觉得自己的三十岁，物质上没有『立』起来，但是精神上是逐渐『立』起来了，因为我是在不知不觉，对自己、社会、世界、他人以及未来开始变得柔软。

当下的我们，处于一个多么美好的时代，我们正在进入发达国家的行列<sup>[2]</sup>，虽然自己也没有发达：没车没房，社会上也屡有不愉快的声音出现在我们周围，但是总体上，我们还是在朝着更好的方向前进。

在这里我也必须要成为『五毛』：我对于我们国家的未来有着盲目的自信，如果用一句简单的话概括我的 2019，那应该就是『赌注已下，静待结果』。

利益相关：重仓持有代表国运的相关股票指数基金。

### P.S. 值得一读

-   《黑匣子思维》：[如何理性地失败：黑匣子思维](https://github.com/xizhibei/blog/issues/106) ；
-   《曾国藩传》：[大清王朝最后的领航者：曾国藩](https://github.com/xizhibei/blog/issues/122)；
-   《大江东去》：这是小说，挺长的，年初没有看电视剧，直接看了原著[读后感](https://github.com/xizhibei/blog/issues/99)；
-   《褚时健传》：褚老的人生习惯值得学习，特别是出狱后在哀牢山中种出了冰糖橙，更是让我深受启发，科学管理以及敢于折腾；
-   《关键对话》：上面提到了，沟通是软实力中最关键的一项；
-   《上帝掷筛子吗》：量子物理的发展史，科普读物，让你了解到目前人类最先进的科学前沿在哪，以及明白《生活大爆炸》里面的 Sheldon 的工作到底是干什么；
-   《被讨厌的勇气》：上面也提到了，我们所经历的一些，不会决定我们，再怎么找原因，也不会改变我们，我们赋予了过去什么意义，就会回决定自己的生活，改变的力量一直在我们手里，我们缺乏的，一直是被讨厌的勇气；
-   《乔布斯传》：原来乔帮主那么爱哭，而 Flash 的死亡背后，还有乔大爷与 Adobe 的『世仇』原因。用户从来不会知道他们想要什么，直到你把产品送到他们面前；
-   《创新者》：《乔布斯传》同作者，我觉得更好看，创新来源于碰撞、颠覆与不循规蹈矩，另外还有站在巨人的肩膀上；
-   《OKR 工作法》：学会用 OKR 的方式工作，[OKR 实践](https://github.com/xizhibei/blog/issues/111)；
-   《横向领导力》：敢于站出来承担责任的，就是领导；
-   《架构整洁之道》：Uncle Bob 的又一大作，不用多做解释；
-   《The Rust Programming Language》：集现代高级语言的优点于一身的语言，连微软都决定要用它取代 C++ 了，学习的过程中会发现各种语言的影子，这本书是官方的电子书，线上就能看，对初学者非常友好；

### P.S. 微信订阅号

其实几年前就开通了自己的微信订阅号，但是至今从来没有发过一篇文章，这里就贴出二维码了，欢迎关注交流，我不能保证更新频率，只能说至少保持与这里个人博客的更新频率一致，在我觉得订阅号更好之前，我还是以这里为主。

搜索习之北或者 xizhibeiblog 应该也可以。

![mygzh](https://blog.xizhibei.me/media/15701774378793/mygzh.png)

### Ref

1.  [三十而立，“立” 的是什么？][1]
2.  [要做困难的事情才能成为发达国家][2]

[1]: http://m.xinhuanet.com/2017-08/03/c_136493364.htm

[2]: https://www.guancha.cn/ningnanshan/2019_10_09_520604.shtml


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/128 ，欢迎 Star 以及 Watch

{% post_link footer %}
***