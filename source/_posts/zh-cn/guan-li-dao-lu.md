---
title: 管理道路
date: 2016-08-07 12:33:38
tags: []
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/25
---
很多同行都会想，当自己做到一定程度了，应该会去做技术专家，或者管理岗位。

只是，现在的很多同行是这样的，我很喜欢钻研技术，并且，我还想往管理岗位上发展，但是遇到一些团队管理的时候，往往不想去处理。

原因？很简单，因为搞定团队内部的琐事，把团队带的井井有条，远远比不上自己去钻研新技术，提高团队的生产力带来的快乐要多。所以，这样的人，不太适合往管理岗位发展。而管理岗位做的好人，很大一部是在技术上已经或者从来没有快乐的人去做的。每个人都会倾向于自己得心应手的那部分，快乐还是非常重要的，在自己的舒适区里面，快乐是永恒的，付出很少就能得到很多回报。

但是，从另一个方面来说，做技术的人，往往倾向于听从于技术能力比自己强的领导：沟通简单，很多事情说几个术语就能让领导理解，领导也能从过往的经验给出一些意见跟建议。

所以，就这样了？

显然不是，并不是就这样没戏了。

当技术做到一定程度，你就可以尝试带小弟了，一个关键的点就在于，你愿不愿意 ** 跳出自己的舒适区并且承担责任 **：你的时间不再仅仅属于你自己，你得花时间教他们，帮团队解决基础设施问题，跟领导沟通汇报，团队内部有问题了及时解决，有时候，你可能一整天都会在处理各种各样的琐事上面，只有下班之后才能做自己的事情，看点书写点代码之类。

也许，初期你会觉得不值得，有点浪费时间，还不如自己上呢。没错，现在的我就是这样，很多东西自己做的效率非常高。只是，这样的话，下面的人得不到锻炼，他们的效率提不上了，你可以说招更多的牛人一起共事，有钱的公司当然可以这么干，但是为了成本以及长远考虑，肯定需要自己去招一些技术能力一般，但基础不错的，培养他们成为牛人的成就感不会比你解决一个技术难题少。更重要的是，以后这帮人是可以帮你做事的，一个人再牛逼肯定比不上一个好的团队。

你要硬扯一些特例，我也明白，牛人是有，甚至作战能力秒过很多团队，这是战术，而公司更多需要的是战略：牛逼的团队。

所以，成为牛叉的技术专家还是带出一个牛叉的技术团队，都可以选。

我自己的话，觉着，挑战下其它领域也挺有意思呢。** 一个人可以走的很快，但是带起一个好的团队，可以走的更远 **。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/25 ，欢迎 Star 以及 Watch

{% post_link footer %}
***