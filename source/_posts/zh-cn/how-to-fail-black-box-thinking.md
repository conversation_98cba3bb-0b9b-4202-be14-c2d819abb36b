---
title: 如何理性地失败：黑匣子思维
date: 2019-05-20 12:31:26
tags: [企业文化,管理,鉴书]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/106
---
<!-- en_title: how-to-fail-black-box-thinking -->

《黑匣子思维》这本书是很早之前就看过一部分的，不过忘了什么原因没看下去。这个月把这本书重新拿出来看的时候，好后悔，当初为什么不把它看完。这些天看完后，觉得我之前在 [谈谈服务稳定性][service-stability] 提到的一些内容与观点，相比之下都过于粗浅了。

相信你在看到黑匣子这几个字的时候，会联想起飞机，我们知道，在航空业中，每架飞机都会装两个几乎无法被破坏的黑匣子：一个记录着发往机上电子系统的操作指令，另一个记录着驾驶舱内的对话与声音，一旦事故发生，黑匣子记录的数据就会被取出分析，事故原因也就一目了然。

这跟我们今天说的内容，有什么关系？现在，不妨让我们从一次作者在开篇提到的手术事故说起（以下内容涉及剧透）。

<!-- more -->

### 一次手术事故

马丁的妻子伊莱恩，因患鼻窦炎多年，医生建议她进行手术进行根治，由于医生的水平很高，医院的设施也是一流的，他们俩对手术并不怎么担心。

只是当她被推入手术室，被麻醉后，问题出现了：她的下颌的肌肉过于紧张，导致用于帮助呼吸的喉罩无法放入，于是医生增加了肌肉松弛剂的量，之后又换上了更小型的喉罩，但都无济于事。

这时候血氧饱和度降到了 75%，大大低于严重偏低值 90%，医生决定气管插管，只是由于无法看到呼吸道的位置，于是一直无法成功，这时候血氧值降到了 40%，如果再继续下去，大脑以及其它气管的氧气就会供应不足，导致损伤。

这时候还可以进行气管切开，然而医生却仍在尝试插入气管，即使是有经验的护士把切开气管的工具拿了进来，医生却还是在尝试，但是由于护士不是权威，不敢提示医生。

最后，医疗事故就这么发生了，即使医生最终将气管插入，血氧恢复正常，但由于时间过去了太久，伊莱恩的大脑还是遭受到了严重损伤，经历 13 天的昏迷后，最终还是去世了。

马丁在之后听到的原因只有：『麻醉过程中出了一些问题，我们尽力了』。他没有愤怒，因为他非常信任医生们的专业，但作为一个飞行员还是会充满疑问，他非常不理解，因为在他所从事航空业里面，他们对待每一件事故都是非常严肃的，会抱着公开以及坦诚的态度进行非常细致的调查，并把每一次事故都当做进步的机会，但医疗行业，为什么会是另一个样子？这起原因到底是什么？为什么他们不肯公布原因？于是他开始推动这件事故的调查。

补充一个知识点：航空事故发生的概率，从 1958 年来一直非常低，2012 年的统计结果是每 470 万次飞行中，发生事故的次数才会有一次<sup>[1]</sup>，而且发生率是历年来逐年下降的。有兴趣的也可以去看看[统计数据][1]，这里非常细致地记录与分析了历史上的每一次事故。

### 简介

作者是马修 · 萨伊德，在这本书中，他用丰富的事例，从心里、经济、管理、文化等角度入手，分析了成功的经验与失败的教训，告诉我们『如何聪明而有意义地犯错』。

在我们传统的思维里面，我们会详尽办法避免失败，而作者却告诉我们，如何更理性地对待失败，来让我们更好地成功。

### 几个观点

接下来说说，书中的几个观点。

#### 闭路循环与认知失调

古代流行着一种叫做放血疗法的医疗手段，一度被认为非常有效，活下来的人都会认为放血很有用，而死去的人则会被认为病太重，放血都救不活了。

再加上上面故事中的背后，都有**闭路循环**的存在：不容许质疑、不容忍失败，导致隐瞒失败，失败会被剔除于系统之外，我们需要从中学习的关键信息就这样被毁掉了，于是整个系统都无法进步，甚至更加失败，这其实也是一个恶性循环。

一直以来，我都挺讨厌星座学说，作者也告诉我们，星座占卜学说都是伪科学的原因：他们的特征很简单，不能证伪，不容失败与质疑，于是无法进步，我们现在的星座占卜学说还是跟几百年前一样，而那句戏虐的话**真正的科学总是从正确走向错误**，恰恰说明真正的科学是一直在发展的。

我们的社会很多时候都不容忍失败，于是在那个时候的医疗行业里面，每次事故发生的时候，大家都互相指责，急于摆脱承担错误的压力，因为一旦承认错误，就可能意味着失去工作，甚至被告上法庭，可能失去医疗执照等等后果。从心理角度而言，没人愿意坦白，大家都会急于隐瞒、否认事故的真正原因，用不可控的因素来掩盖，甚至与其他人互相帮助隐瞒。你的良知或者说态度告诉你不该这么做，但你还是这么做了，导致之后出现了心理不适，这就是所谓的**认知失调**。

#### 边际收益与精益思想

作者也提到了失败在精益思想中的作用，我们不可能一次性就做出完美的产品，我们就需要一次又一次迭代，迭代中会出现非常多的失败，这些失败让我们不断提升认知与进步，从而做出越来越好的产品。

这个过程中，不断累积的进步，其实就是边际收益。

我们如今 AI 越来越强大，知道点原理的人也能够明白，整个训练过程其实都是基于不断尝试，好的训练结果，背后也会经历很多失败。

#### 固定思维与成长思维

作者在最后说明了两种思维的人，固定思维者与成长思维者。

有人能从错误中吸取教训，从而不断成长，而其他人却无法面对失败，这其中原因就是思维方式的不同。

固定思维者就无法面对失败，他们往往会把失败归因成自己的能力不足，而且认为以后也很难有改善的可能，面对失败情况，往往会选择逃避：归因成外部不可控因素，从而获得内心的舒适；而成长型思维者，也可能会归因为自己能力不足，但是他们同时也会认为能力是动态的，自己可以改变与进步，失败就是其中的秘诀，它并没有那么肮脏和羞耻，它必不可少。

#### 自我妨碍

其实就是怕失败，害怕承认失败，害怕承认自己能力不够。

回想下我们以前学校里考试的时候，学霸们总是在考试前说自己学得不好，要挂科了，结果却是他们考得最高。

你想过他们为什么要这么说么？其实就是因为假如考得不好，他们就可以用没有准备好来安慰自己了，而并不是他们的能力不足，而考得好的话，还会偷着乐。由此，不需要承认自己的失败，就可以安心考试了。

这与上面说的固定思维也有关系，往往就是因为害怕承认失败，害怕失去脸面。

### 我们的 IT 行业

回到我们的行业，说说我们做的线上服务，现在看来，我们就是要**容忍失败**。

回想当初劝上级别惩罚搞出事故的责任人的时候，我其实就应该用这本书里面的故事来好好说说，惩罚只会让责任人害怕犯错，更可怕的是，这种态度会让整个公司失去该有的进步机会与创新精神。

而创新精神，从某种意义上来说，就是不断尝试，不断失败。只有当我们承认我们会失败，敢于面对失败，并从失败中学习的时候，我们才能进步与成功。

因此，『我们的线上的服务很重要，不能犯错，犯错的成本很高』，当上级说出了这些类似话的时候，可能也就意味着整个公司将会或者已经形成一个不允许失败发生，不容忍失败的环境与文化，于是意味着将会失去更多更重要的东西了。

现在每当看到媒体上有公司里面的管理者在说，他们对待错误是如何如何严肃，将会如何如何惩罚工程师的时候，我都会觉得很惋惜，多好的公司，就这么放弃进步的机会了。

另外，容忍失败并不意味着容忍蓄意妄为、明知故犯，比如飞行员在驾驶前不能饮酒，而对于我们工程师不能违反操作规定随意修改生产环境的配置或者数据库。

最后，对于解决的方案，作者在书中说明了解决问题的一个办法，一定程度上跟[我之前那篇博客][service-stability]说过的有相似的地方：

> 要建立一种制度，我们可以把错误理解成期望与现实之间的差距，处于前沿的机构一直寻求消除这种差距，但他们必须先建立一种制度，以便随时抓住机会进行学习。
> 仅仅有一套良好的制度还不够，如果从业人员不去分享有用的信息，再完善的制度也无法产生效果。

总结来说，其实就是**建立容忍失败的文化，以及事后分析的制度**。

其实说到这里也差不多了，对于我们自己来说，这个世界跟我们的认知总是有差距的，想要成功，就从承认自己会失败开始。更多内容，推荐好好看看这本书。

### Ref

1.  [Causes of Fatal Accidents by Decade][1]

[1]: http://planecrashinfo.com/cause.htm

[service-stability]: https://github.com/xizhibei/blog/issues/76


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/106 ，欢迎 Star 以及 Watch

{% post_link footer %}
***