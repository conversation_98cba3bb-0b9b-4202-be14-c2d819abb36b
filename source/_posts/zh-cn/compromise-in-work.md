---
title: 工作中的妥协
date: 2017-09-24 00:30:13
tags: [工作方法,沟通]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/58
---
<!-- en_title: compromise-in-work -->

我觉得，这一两年我学到最有意义的工作方法，便是：**妥协**。

### 招聘

以前经常说，[一定要招聘优秀人才](https://github.com/xizhibei/blog/issues/36)，不优秀的不能接受，这原则本身没有问题，问题在于，我们把很容易把缺乏实际开发经验的『不够优秀』的职场新人也淘汰了。现在回头想想自己，当初我们刚入职场的时候，不也是菜鸟一个么。

这里的问题就在于，我们不能接受培养新人接手我们工作的做法，不肯拿出自己的时间去培养他们。当我们强调只招聘优秀人员的时候，往往容易忽略了容人之短，这导致一个直接后果就是招不到人员。

所以当我们回过头来，在招聘的 JD 上面，经验一定是死条件么？显然不是，在你所在的公司可能不那么有名气或者拿不出太多诱人条件的时候，对待刚毕业的职场新人，**基础扎实，能力优秀，可培养**其实就完全可以考虑。

### 人力、需求以及时间

用最近常逗人一笑的『我们是谁』来说：

> 我们是谁？
> 需求方！
> 我们的需求是什么？
> 不知道！
> 需求什么时候要？
> 现在！

当海浪一般的需求，一波接着一波向你涌来的时候，你需要忍住手撕对方的冲动，对的，即使对方是定了上线时间就在下周一，你也要忍住不去动抽屉里的那把菜刀。让需求方搞清楚他们的需求，以及让他们妥协点时间，要不就是砍点需求，因此而这个过程中，肯定需要妥协，而这也需要你发挥自己的沟通以及协调能力。

不过，在实践 BDD (Boss Driven Development) 以及 TDD (Treat Driven Development) 的公司里，做到这点还是很难的，毕竟这涉及到教育 Boss 了 :P，相信敏捷开发的路是你们可以考虑的。

### 技术债务

常听到的一句话便是：『给我们俩星期重构吧，停一下，现在的系统太烂了，欠的技术债务太多了』。

从开发人员的角度，的确，技术债务不能欠太多，只是，这也要看什么时期，对什么人说，当业务在高速发展的时候，这句话是不能随便说出口的。

尤其当时你们处在活下去最重要的创业初期，CEO 在那里说，我们需要尽快改版，开发出核心功能，你却跟他说出这句话，无异于你想把空中的飞机停一下来进行维护。这时候，你不妨站在 CEO 的角度，当你的公司生死未卜时，技术人员跟你说出这句话的时候，这是不是相当于在说：『我们不干了，让公司破产吧』？而当你的公司处在竞争激烈的领域，2 个星期的差距完全可以让你们落后于竞争对手，造成非常大的差距，所以你想把他拉出去枪毙 10 分钟还是 20 分钟？

这个时候，只能妥协，公司活下来最重要，没必要花太多时间去追求高大上的技术，更没必要优化代码跟架构到什么业界高标准的程度，而且，架构是不断演化出来的，一个可以参考的简单标准就是：**在可预见的将来，可以正常使用**。

因此，你们不妨在开发迭代的周期中，制定相关的规划，加入技术债务的偿还需求，分步骤一点一点偿还。假如项目经理不理解这些需求的话，就需要你去沟通解决这个问题了，让对方明白这个需求的重要性，相信一个有经验的技术项目经理是能理解的。

君不见，**那些年的创业公司，真正活下来的才有继续优化与发展的机会**。

### 新技术

作为技术人员的我们，总是会想在我们的项目中应用最新的技术，这本身无可厚非，而且，这也有助于提升公司的创新能力。

但是，我们需要衡量引入新技术的成本与价值，而这时候，我们也需要站在公司的角度上来思考，这项新技术对于公司的价值在哪？

-   学习成本低？1 周内可以上手？
-   节约开发时间？节约多少？
-   社区活跃？学习的开发人员多？

另外，在创业初期，『快、猛、糙』的技术反而是最实用的，公司里负责技术的人最熟悉的技术便是我们应该采取的。别忘了，**技术的真正价值在于为产品服务**，如果忽略了这一点，就该重新思考下公司的命运了。

> 这里有一句题外话，给 Node.js，Ruby，Python 等脚本语言打个广告，它们的灵活性能让你们可以在创业初期快速开发、迭代、试错。当别的 Java 团队在用十个人在那需要三个月开发功能的时候，你们两个人就能前后端在一个月内开发出原型了。

### 重点

敲黑板，现在我们开始划重点。

其实上面说了半天，关键还是在于**沟通**，毕竟妥协需要通过沟通来达成。无论是我们是在创业公司还是上市大公司，我们总是会遇到各种问题，令我们不满，甚至偶尔还会抱怨，但是，这只是消极面对，只是逃避，不能解决问题本身。

发挥我们的主动性，主动去思考，分析原因，讨论解决方案，赢得身边同事还有领导的支持，最后就能顺利解决问题。

另外，我觉得妥协便是韧劲，是一种曲线救国的思想，能够**让你在环境对你不利的时候，还能把事情做成**的方法。不知你是否还记得中学里的材料作文：在狂风过后，坚硬的大树折断了，柔软的小草却依然挺立。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/58 ，欢迎 Star 以及 Watch

{% post_link footer %}
***