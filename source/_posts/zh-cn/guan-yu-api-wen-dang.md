---
title: 关于 API 文档
date: 2016-06-02 12:58:18
tags: [Node.js]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/18
---
先从一次面试经历说起，记得有次面试，我问被面试者一个 API 文档的问题：

> 『用 gitbook。』
>  我呆了一下
> 『那如果你们修改了 API 接口，怎么修改文档呢?』
> 『我们文档也用 git 管理，直接修改提交。』
> 『那你们有没有某次代码修改之后，忘记修改文档了?』
> 『……』

我想在实际开发中，这样的做法很容易顾此失彼，忘记更新代码，造成了文档与代码不一致的情况。

众所周知，好的文档能提高使用这个项目的工程师的开发效率，相信你也遇到过这样的情况，某个开源项目，照着文档实现后发现结果不理想，甚至无法运行的情况，然后只能去看代码。（其实从另一个角度说，这也能逼着使用者去看代码，理解原理。。。）

只是，在同一个项目里的时候，APP 工程师不用去看服务器工程师实现的代码吧？于是一份好的文档能省不少事，提高各自的开发效率。如果你遇到过，Android 跟 iOS 开发工程师分别来问你 API 接口的情况，你就很能理解了。

一个我认为理想中的跟 APP 工程师协同开发流程是：先跟所有人，包括产品经理一起看文档，弄清需求，分拆任务，约定好接口，参数之类的。然后服务器工程师按照约定写一个 mock API 提供给 APP 工程师，之后两边各自开发测试，直到最后联调通过。

其中约定好的 API 接口，由于在开发过程中可能会改，于是，一个可以动态更新的 API 文档就很重要。有这么几个选择：
- apidoc.js
- Evernote 之类的笔记共享
- gitbook

很明显，我认为将文档跟代码放在一起是最好的选择，文档按照一定的格式写在注释里面（没错，我就是来安利 [apidoc.js](http://apidocjs.com) 的 😆 ），代码一改，直接在代码上面的文档修改，然后在测试环境部署时，加个 pre-update 生成新文档即可，通知 APP 工程师即可。

这样的话，可以避免一定的文档与代码不一致的情况。（当然，如果服务器工程师懒的写文档的话，那也谁也救不了。


***
原链接: https://github.com/xizhibei/blog/issues/18

![知识共享许可协议](https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png "署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）")

本文采用 [署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh) 进行许可。
