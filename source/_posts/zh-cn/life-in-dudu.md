---
title: 在嘟嘟的那些日子
date: 2017-10-08 12:41:15
tags: [创业]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/59
---
<!-- en_title: life-in-dudu -->

不知不觉，『嘟嘟美甲』这个名字，现在已经很少有人知道了，就如那些被淘汰的大多数公司一样。这是一家在 O2O 浪潮之中的一家提供上门美甲服务的公司。

### 眼见他起高楼

记得那时，是毕业一年之后，只是因为在外企呆的太郁闷，不开心，于是，在 CEO 口头给了期权之后，便加入了。应该说这是大学里一直想做的事：毕业之后去一家创业公司。

离开了高大上的办公室，搬到了小别墅里面，楼上睡觉，楼下工作。工资降了，期权给了不少。为了以后，当然想要期权高一点。

那时候全身心投入到公司里面，连我周末不加班的原则都打破了，毕竟，这也是为了自己的将来。

慢慢的，公司开始发展了，于是在公司不远的工业园里面，租了一个房间，是的，你没看错，是一个工业园，我们可能算是唯一的互联网公司吧。幸好地方离得挺近，骑车 15 分钟，每天还是那帮兄弟，还是起早贪黑地工作。

在这之前，我一直在大公司里面用的 Java ，来到这里之后转成了 Node.js。原因么，一个是之前自己就用过，另一个，因为现有项目都用 Node.js 来实现了，而且开发起来非常快。由于自己不是非常熟悉，有几次还把服务器搞挂过，大家也没有过分责怪我，而我也努力不犯同样的错误，而正是这些错误让我很快成长。

慢慢的，我实现了管理后台的重构，帮助运营人员更好更快地操作。然后实现了我们的核心功能，美甲师预约与实时路径规划功能。现在回想过来，这个实时路径规划的算法有机会还是可以拿出来说说的。

那些日子，每天都在学习，在成长，**甚至一周之后，都会觉得上周的代码太垃圾**，然后花上几个小时去重构，渐渐地，我让自己成为了一个全栈工程师。

### 眼见他宴宾客

公司发展非常迅速，美甲师预约量逐步提高，尤其是引入了补贴之后，美甲师一天『刷』个几十单也不成问题。当然了，这成了我们需要解决的问题，我们都调侃说：『不做工程师了，直接去做美甲师吧』。

之后，我们在几个大的城市开始铺路规划，大范围招聘美甲师与管理运营人员，那时候也是我们最累与最乱的时候，需求永远在排期，运营人员的需求永远得不到满足，但是，整体上，我们还是在继续发展。

在年会的时候，看到那么多从几个城市赶过来的同事么，还是有点震惊的，『哇，我们发展那么庞大了啊』。

### 眼见他楼塌了

终于，2015 年，在年末的时候，好多同事不见了，我们开始合并办公室了，也没什么需求要排期了，开始整日整日闲下来了，突然觉得好可怕，我们是要倒闭了吗？

是的，我们没钱可以烧了，2015 年股市危机之后，我们的投资人在股市中赔了，于是，我们没有钱可以烧了。

我们开始寻求收购，由于价值观不一致，我们放弃了泰迪洗衣，最后『被迫』卖给了 58 到家。值得一说的是，我们几个一开始就加入的伙伴，站了最后一班岗，完整对接 58 到家，将系统交接给他们。

最后么，公司正式倒闭，领了离职证明，期权也成了一张废纸，休息了一阵之后，开始找工作。

### 回想

那时候也想过，如果继续待在外企，可能不会那么『落魄』吧。

其实，加入创业公司，就是一次投资，用来投资的东西，是自己。由于风险很高，可能最后你没有什么物质回报，但是，这种工作的激情与经历，却是你在大公司里面，永远得不到的：你完全参与在整个过程中，你是为自己打拼。

而正是在嘟嘟的日子，让我学到了太多东西，交到了一帮兄弟，以及成长了太多。在后来找工作的时候，一度以为自己『不那么值钱』，哪知道几乎去面试过的公司，都给了我 offer。

我后来也总结过：**『在创业公司里面，你一开始就会拥有着比大公司里面更多的资源，也有非常多的机会让你去试错与成长，这才是你能快速成长的关键；而朋友之间最好的感情，不是一起吃喝玩乐，而是来自于一起把事情做好，做成，共患难才是你们最珍贵的经历。』**


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/59 ，欢迎 Star 以及 Watch

{% post_link footer %}
***