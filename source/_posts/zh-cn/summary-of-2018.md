---
title: 2018 年总结
date: 2018-12-31 18:36:26
tags: [总结,年度总结]
author: x<PERSON>hi<PERSON>
issue_link: https://github.com/xizhibei/blog/issues/96
---
# 2018 年总结

<!-- en_title: summary-of-2018 -->

时间过得真快，一眨眼，2018 年要过去了，而 [2017 年的总结](https://github.com/xizhibei/blog/issues/66) 仿佛近在昨日，总体来说，今年感觉是成长有限。

先罗列几个数字：

-   28 篇博客
-   50 本书
-   0 个女朋友
-   6 处旅游地点
-   3 个轮子

显然，被 2018 年给打脸了，肚子没减掉，来了深圳之后，见爸妈的机会也少了，博客内容受欢迎不如预期，主动认识的朋友也有限，女朋友与股市一样：没希望。

好了，脸打完，还是得回顾下自己这一年到底瞎干了啥。

<!-- more -->

### 规律

博客按照自己的规律，保持了两周至少一篇的频率，至今还没有打破这个习惯，当然了，为了保持这个习惯，我也付出了挺多，显而易见的便是在上海的时候几乎每个周末都会去咖啡馆度过，但是还是非常充实的，也算是没有对象的好处吧，一个人的周末便可以按自己的想法来过。

健身习惯也开始养成，比如在之前不冷的时候，保持了每周至少跑步三次的习惯，但是最近天气冷下来就放弃了，不过，当明年天气回暖的时候，我还是会恢复这个习惯的，至少这个习惯让我在 3 个月中减掉了 8 斤。

开始给自己做季度计划了，持续了两个季度，其实这个习惯是年中才养成的，但是目前还是做得不够，因为做成比较大的项目还是比较少，因为一般的说法是，季度计划比较适合用来完成一个中小项目。

### 读书

今年看书比去年多，只是更杂了，这里列几本读了之后觉得不错的：

-   《薛兆丰经济学讲义》：系统地告诉你如何用经济学思维去思考社会现象等，犹如醍醐灌顶，首当推荐；
-   《软技能：代码之外的生存指南》：这本很有用，告诉你代码之外，你还是有很多事情需要做，比如规划自己的工作；
-   《运营之光》：可以了解到你的运营同事们整天都在『瞎忙』什么，理解什么是产品的短期价值，以及淘宝为什么是运营驱动的；
-   《思考，快与慢》：人有两种系统，系统 1 处理信息快，靠直觉就可以完成，系统 2 需要比较耗时间，当遇到系统 1 处理不了耗时任务，比如估算时，就会用到；
-   《反脆弱》：告诉你如何应对黑天鹅现象；
-   《咨询的奥秘》：老板不喜欢你告诉他这个需求做不了，你要告诉他这个任务做得了，然后成本是多少，让他自己选；
-   《横向管理》：不是领导，怎么管理自己的同事与上级？
-   《创新者的窘境》与《创新者的解答》：前者告诉你为什么诺基亚会失败，以及后者告诉你怎样避免失败，成功了之后所拥有的东西反而会成为束缚，进而成为日后失败的原因；

### 开源项目

如 17 年总结中所想要做的，我把分布式任务队列的轮子做掉了：[blackfyre](https://github.com/xizhibei/blackfyre)，但是没有给它做过宣传，因为担心自己做的不够好。

事实证明我确实做得不够好，因为做开源项目的思路不对，比如后来为了能够更方便地使用个推的接口做了个 [getui-rest-sdk](https://github.com/xizhibei/getui-rest-sdk) 而且给它在[使用 TypeScript 开发 NPM 模块](https://github.com/xizhibei/blog/issues/68) 给它间接做了宣传，效果稍稍好了一点点，只是相当于没有。

吸取教训后，我在第三个轮子[grpc-helper](https://github.com/xizhibei/grpc-helper) 开发完成后，用了[gRPC 的介绍以及实践](https://github.com/xizhibei/blog/issues/84) 以及非常明显的广告 [如何在 Node.js 中更优雅地使用 gRPC：grpc-helper](https://github.com/xizhibei/blog/issues/86) 给它做了宣传后，效果明显好了一些。

总结来说，我觉得做开源项目犹如创业，光懂得把东西做出来还不够，还需要投入精力『卖』给他人用，也就是营销，把项目当成产品来开发，只不过你的用户是工程师同行，所以想要做好开源的轮子，就需要不断投入精力，与『用户』们交流，并及时反馈，取得他们的信任。从这点来说，国内开源产品目前 PingCAP 是很不错的，有实力，会营销，真正把开源项目做成了产品。如果你想要真正做好开源产品，会营销也是一个重要的成功因素。

### 工作

在 10 月份的时候[换工作了](https://github.com/xizhibei/blog/issues/92)，本来想在离职的时候好好吐槽下，但是忍了一个月才把一些不重要的内容给忘掉了，是的，这种时候，得多想想自己的问题在哪儿，而不是一个劲抱怨环境。

可能今年觉得最开心的事情就在于走之前领导找我的谈话了，肯定了我的贡献以及努力，一定程度上我理解为送我离开前的一些套话，只是，我认为如果以后我再遇到我的下属离职，我也会说这样的话，算是种好聚好散的方式。

同时，对他人的肯定不仅仅需要这种时候做，更需要在平时做。

### 管理

上半年在管理上能把一些学到的东西付诸实践了，比如**一对一会谈**以及**利用会议来解决一些跨部门沟通的问题**。

在一对一会谈的时候，我深感这种做法的好处，因为很多事情没法站在他人的角度来理解，一对一的时候反而能说清楚，而我也会用我的角度告诉他们有哪些地方可以改进。在年初的时候，就通过这种方式，了解到了其中一个想走的想法，通过不断交换想法，虽然最终很遗憾没有留下对方，但至少能让我提前知道这件事情了。

而我用会议来解决跨部门沟通的时候，似乎是有种受影响的意思，因为当下属在群里吐槽其它部门的人不会做事的时候，我第一个反应就是让他组织个会议，叫上我跟对方一起讨论下问题。这在以前我是很讨厌开会的情况下是不可能的事情，这大概是意识到沟通的重要性了。

### 技术

今年开始大量投入精力在 TypeScript 以及 Golang 上面，因为深深觉得强类型语言是一种必然，或者说是维护过 Node.js 大型项目之后，经历了那么多痛楚后的感悟。然后这又有个『窘境』：因为前期不用 Node.js 的话，可能强类型的不灵活会造成不灵活的业务变动，没准让创业公司根本活不下来。

另外就是微服务，今年终于将 Kubernetes 结合到微服务拆分的过程中去了，也用了线上流量进行灰度测试，效果感人，最明显的莫过于服务器数量的减少了。也踩了不少坑，比如 [Kubernetes 排错之 HTTP 429](https://github.com/xizhibei/blog/issues/72) 。

在微服务上面，有不少的总结：

-   [分布式追踪](https://github.com/xizhibei/blog/issues/74)
-   [Go kit：Golang 微服务的瑞士军刀](https://github.com/xizhibei/blog/issues/78)
-   [微服务设计模式之断路器](https://github.com/xizhibei/blog/issues/78)
-   [微服务设计模式之 API 网关](https://github.com/xizhibei/blog/issues/82)

另外还针对 Kubernetes 的运维写了 Helm 的『三部曲』：

-   [Helm 入门之基础](https://github.com/xizhibei/blog/issues/89)
-   [Helm 实践之配置管理](https://github.com/xizhibei/blog/issues/90)
-   [Helm 实践之持续交付](https://github.com/xizhibei/blog/issues/91)

### 对外交流

这方面交流不够，有点井底之蛙的意思，偶然间在 GitHub 上发现了即刻 APP 的团队，他们也是 Node.js 技术栈，而且我也经常用他们的产品，于是主动联系了他们，叫上了公司同事一起去。

交流了之后，发现自己确实是个井底之蛙，他们在技术实践方面挺深入的，而且团队更大，最关键是公司文化确实挺让人羡慕。

总之，与他们接触了后才明白，什么是『快乐的团队，快乐的产品』。

### 理财

18 年有太多的泪了，其实不说也懂，大量的风险资产被抛售，股市一片哀嚎，我年初很兴奋地跑进美股，结果大家都懂的，幸好自己的风险偏好不高，损失勉强能接受。

楼市就别说了，反正暂时与我无关了。

同时，我想说，当所有人都在退场的时候，就是可以考虑入场的时候了，这话点到为止，不要理解为鼓励你去抄底，风险还是很大的，当前还是现金为王。

### 旅游

今年去了 6 个地方，深圳、北海、南宁、南京、桂林以及沙巴，或许之后的博客中，会出现游记。

### 2019 年 Flags

1.  学会用 OKR 管理自己；
2.  进一步营销自己；
3.  继续减肥，让自己更健康；
4.  继续旅游，写游记；
5.  继续读书，写读书笔记；
6.  丰富周末生活；

### P.S.

博客已经 116 个 Star 了，挺开心的，就是有着这种简单的幸福感，让我能继续坚持，总之，多谢各位捧场。

最后，祝大家新年快乐。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/96 ，欢迎 Star 以及 Watch

{% post_link footer %}
***