---
title: 谈重构
date: 2016-07-09 14:53:44
tags: [重构]
author: x<PERSON>hibei
issue_link: https://github.com/xizhibei/blog/issues/22
---
最近总能发现，很多东西，我在实践中总结出的的经验，都是别人在书中提到过的，就比如《重构》这本书，我一直认为重构不应该拿出专门的时间，而是应该在开发新功能或者修改 bug 的时候去做，作者也非常强调这种观点：

> 不用专门时间，随时随地，比如以下三种情况：
> 添加功能时
> 修补错误时
> 复审代码时

比如在一个人员缺少的阶段，如果你跟产品汪或者 CEO 说，我需要几个星期甚至几个月去专门重构下，相信他们砍了你的心都有了。不是说这时间不该花，而是，这个时间可以算在功能开发上面，你跟产品沟通清楚：『
我们开发的这个功能是基于某个以前的功能的，但是需要改进它，这样的话，以前的功能可以更稳当或者更快，然后呢，以后的相关开发可以更快更好，并且这个重构只是将这个功能的开发时间延长了一到两天』，让产品汪明白重构的价值之后，相信他们也会理解到这个时间是必须给的。

另一个小的原因在于，你在重构之后，大脑正处于高度集中状态，会对相关的代码以及功能非常熟悉，开发也会快一些。

然后再谈谈其它的几点：

> 坚持以持续不断的重构行为来整理代码

我觉得代码洁癖是每个软件工程师应该具备的素养，当你看到别人或者自己一两个星期之前写的代码感觉很烂的时候，就应该做点什么了。

> 在不改变代码外在行为的前提下，对代码做出修改，以改进程序内部结构

此时最适合用 TDD 了，以前偷懒欠下的债，这时候改还了，把单元测试补上，然后开始重构。测试是重构的灵魂啊，把 CI 搭起来吧。

> 三次法则：
> 第一次，只管去做，第二次做类似的事，会产生反感，但无论如何还是可以去做，第三次再做类似的事，你就应该重构。

这个说的应该是完美心理，跟代码洁癖是一起的，应为洁癖来自于完美心理，只是为了效率来说，刚开始可以不用考虑太多，做出来功能是最重要的，然后通过功能的表现来慢慢重构，因为，好代码往往诞生于来自于重构之后。
#### PS：

虽然觉得别人已经总结出了经验，但是，自己去发现不是更有意思点么，就像看电影，如果直接告诉你结局，那这部电影你可能不会想去看了，更何况，提前看了也不一定马上能理解。

当然了，多看这种书没坏处，毕竟人一生那么短，有些弯路也没必要再走一遍。


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/22 ，欢迎 Star 以及 Watch

{% post_link footer %}
***