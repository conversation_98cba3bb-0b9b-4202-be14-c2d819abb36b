---
title: 记一次 mongodb 索引紧急事件
date: 2016-05-28 22:08:25
tags: [MongoDB, 数据库]
categories: [数据库,MongoDB]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/17
---
今天正在家里加班的时候（哎。。。不多说。。。），突然接到电话，mongo 出问题了，同事把某个 collection 的索引给删了，新代码上线之后，mongo 不断在重建索引，经过立马回滚代码之后，还是继续在重建索引，用 db.currentOp 来看，索引一到 99% 就重建，也就是重新开始建立索引。

在 APM 上面看到响应时间慢了 5 倍左右，一直不断重建索引的影响很大。我第一直觉是 mongo 在不断执行重建索引的命令，于是吩咐同事把 schema 里面建立索引的语句删掉，再观察。过了几分钟之后，还是不行，猜测可能是有排队的 op，于是 killOp，立马正常了，再查看索引，已经建立完毕，响应时间也恢复正常。（找时间测试下具体原因，也许是 mongo 的 bug）

这次事故，应该说暴露出了我们运维方面的漏洞，相信同事非常自责。不过，我觉得这不单单是他一个人的责任，团队管理上面问题更大，一直在说要限制每个人的生产服务器权限，但是一直没时间做，觉得业务开发更重要，今天就暴露问题了。

因此，我觉得，当团队发展到一定程度，可以是招新人的时候，一定要限制新人的生产服务器权限。然后，建立 staging 环境，这个权限可以给高点，方便所有人测试，所有的操作最好先 staging 环境测试再到生产环境使用，同时，也给新人机会去学习。

然后，数据库服务器最好一开始就设置权限，禁止非生产服务器之外的 APP 连接，drop database 这样的事故发生过不止一次。同时，这也是方便以后，不然以后从没有权限迁移到有权限也是很头疼的一件事。

下次谈谈 staging 环境建立的事儿。


***
原链接: https://github.com/xizhibei/blog/issues/17

![知识共享许可协议](https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png "署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）")

本文采用 [署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh) 进行许可。
