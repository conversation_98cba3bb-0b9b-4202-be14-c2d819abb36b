---
title: 从把 SLA 加进 KPI 考核说起
date: 2017-07-09 18:01:23
tags: [企业文化,沟通,系统思考]
author: xizhibei
issue_link: https://github.com/xizhibei/blog/issues/53
---
最近公司的领导开始重视服务器的稳定性了。

这是件好事: 因为受到重视，意味着这方面有领导关心跟过问了，对于提高服务器稳定性，大家也都会有更多的压力与动力去做好；这又是件坏事：毕竟是由于历史上出过问题，才导致的，也就是说，之前大家做得不好。

SLA 这个数值很有意义：让大家对服务水平进行承诺，对于公司的减少损失很有帮助。只是对我来说，这件事本身让我有些反感：上级想把 SLA 写到 KPI 每月考核里面，且不说 KPI 考核对于创造性从业者的各种伤害，单是每个月将服务器稳定性加入每月考核 KPI，就有些不合理了。

### 原因

开始说之前，先说下背景：

1.  上级想要达到每个月 SLA 99.7%，也就是说每个月最多服务不可用时间不得超过 2.16 个小时；
2.  当前没有给出任何成本预算；
3.  运维与开发一起背这个 KPI，但是责任要分开；
4.  对于 SLA 的细节没有清晰制定，而目前对于不可用时间，包括但不限于：网络问题、CDN 问题、服务器宕机、客户端 API 平均响应时长超过 5 秒、API 请求错误率超过 4%；

显然，这是我们的责任所在，我们必须要达到上级想要达到的稳定性方案，只是这种方式并不合理：

首先，**制度不合理**：这是一种只见树木而不见森林的做法，假如这个政策制定下来，我们会做什么？显然，稳定大于一切，其中包括各种产品创新，也就意味着开发以及运维会本能排斥任何变动，不敢也不愿做任何大的改动。而当他们遇到问题的时候，相关的责任人可能会尝试隐瞒事故，不让上级跟同事知晓，给以后造成以后更大问题埋下隐患。同时，开发与运维责任分开，会造成以后的互相扯皮、推卸责任，留下不信任与不配合的裂痕。

其次，**时间周期不合理**：举个我们十分不愿意出现的情况，假如接下来一年中，只有一个月的某一天出了个 12 小时问题，而其他月份没有问题，是不是意味着那个月大家的工资都要被扣完了，而其它几个月，我们什么都不用做就能拿奖金？那 KPI 的意义何在？再比如，今年每一个月的 SLA 都只有 99%，而我们花了十个月的功夫，积极解决问题，适应变化，将来年的 SLA 达到了 99.99%，那是不是意味着，今年大家都要在不开心的情况下度过了？

再其次，**责任范围不合理**：服务器稳定性不是单独的开发与运维能够完全决定的：公司内部所有人员的素质，流程规范程度以及投入的成本都能影响结果。换句话说，SLA 完全体现了整个公司的服务水平，也需要大家共同承担。

最后，**成本投入不合理**：防范错误也需要成本，99.7% 看起来固然不高，但是到了这个程度，每提高一个千分点，都意味着背后异常艰辛的努力，以及公司各种资源的投入。

### 应该如何做

**积极处理**：我认为之前的 Gitlab 的删库事件给了我们一个非常好的启示，**尽快将问题暴露出来，积极处理，及时同步给公众，结果不仅非常迅速地恢复了服务，更赢得了公众的认可与信任。**

**共同努力**：我们相信，这个 SLA 是公司里面大家互相配合与共同协作才能达到的结果，因此我们需要在内部创造一种文化：鼓励大家积极创新，快速恢复，为提高服务水平而共同努力。

**系统思考**：公司是一个整体，制定考核指标的时候很容易陷入局部最优，**『把一头大象分两半不会得到两头大象』**，从整体上分析 SLA 在公司的位置更容易知道如何制定合理的考核指标。

### 最终方案

毕竟我们需要承担责任，KPI 也不能完全说不好，针对于上级对于稳定性的要求，我们采取了以下方案：

-   规划：我们接受 SLA 作为半年度考核指标，并且对当前所有的稳定性风险进行分析，看看那些地方能改进，以半年为周期，制定目标，进行改进；
-   记录：记录所有的稳定性问题，给出每一次问题的分析报告；
-   行动：对于所有的改进方案，分配到所有的责任人身上，并按周以及月进行总结汇报；
-   回顾会议：以季度跟半年为单位，在会上回顾我们发生了那些线上问题，采取了什么方案，效果如何，接下来我们应该怎么做，对做的好的地方如何保持，而差的地方如何改进等等；

### 以 Netflix 企业文化中的一段话结尾

> In general, freedom and rapid recovery is better than trying to prevent error. We are in a creative business, not a safety-critical business. Our big threat over time is lack of innovation, so we should be relatively error tolerant. Rapid recovery is possible if people have great judgment. The seduction is that error prevention just sounds so good, even if it is often ineffective. We are always on guard if too much error prevention hinders inventive, creative work. 

### Ref

1.  <https://en.wikipedia.org/wiki/Service-level_agreement>
2.  <https://jobs.netflix.com/culture>


***
首发于 Github issues: https://github.com/xizhibei/blog/issues/53 ，欢迎 Star 以及 Watch

{% post_link footer %}
***