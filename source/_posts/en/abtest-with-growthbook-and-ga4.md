---
title: How to Build a Data-Driven A/B Testing System with GrowthBook and GA4
tags:
  - GrowthBook
  - Google Analytics
  - A/B Testing
  - Data-Driven
  - BigQuery
categories:
  - Data Analysis
date: 2023-06-23 22:15:50
---

Have you ever encountered these challenges:

- After launching a new feature, you're unsure if it actually improved the user experience? For example, did that new registration flow that your team meticulously refined actually increase conversion rates?
- Product decisions often rely on experience and intuition rather than data? "I think users will prefer the blue button," but is that really the case?
- You want to conduct A/B testing but don't know how to build a reliable experiment system? With so many testing tools available, where do you start?

As a seasoned experimenter in startups, I deeply understand these concerns. After experiencing multiple product iterations and company ups and downs, I gradually realized: making decisions without data support is like groping in the dark. If you have similar concerns, this article is for you. We'll detail how to build a complete A/B testing system using GrowthBook and GA4, making data-driven decisions more than just empty talk.

### Introduction to A/B Testing

A/B testing is a statistical method used to compare two or more versions of a strategy or product to determine which version is more effective. It achieves this by randomly assigning users to different versions and measuring the performance of each version.

So, why should we conduct A/B testing?

- **Determine which version is more effective**: Quantify the performance of different versions through data, such as comparing the conversion rates of old and new registration flows (the proportion from visiting the registration page to completing registration). For instance, you might find that a single-page registration form significantly improves conversion rates compared to a traditional three-step process.
- **Optimize products or strategies**: Make improvements based on actual user behavior. For example, test different pricing strategies (¥99/month vs ¥999/year), feature presentation methods (feature list vs interactive demonstration), or marketing copy (emphasizing features vs emphasizing results).
- **Reduce uncertainty**: Avoid the risks associated with full-scale releases based on subjective judgments. By testing on a small scale (e.g., 10% of users), potential issues can be identified early. For example, when testing a new payment flow, first validate system stability and user experience with 5% of traffic.
- **Improve decision quality**: Let data speak, avoiding endless debates within the team. When discussing homepage design options, different team members may have their own opinions, but A/B test results can objectively show which version has a lower bounce rate and longer dwell time.

Next, let's look at how to build a complete A/B testing system using GrowthBook and GA4.

### Introduction to GrowthBook

GrowthBook is an open-source feature flagging and A/B testing platform that provides:

- **Feature flag management**: Supports boolean, numeric, string, and JSON type feature flags, allowing teams to gradually roll out new features through percentage releases or A/B experiments.
- **Experiment analysis**: By connecting to data sources (such as SQL data warehouses, Mixpanel, and Google Analytics), GrowthBook can query experiment data and generate a reusable metrics library, supporting various metric types including binomial, count, and duration.
- **Custom configuration**: Provides custom fields, custom page Markdown, and custom pre-launch experiment checklists to help businesses adjust the platform according to their needs.
- **Data integration**: Supports various data warehouses and event tracking tools (such as GA4, Segment, Rudderstack, and Amplitude), and automatically generates metrics.

### GrowthBook and GA4 Integration

Integrating GrowthBook with GA4 is very simple. You just need to configure BigQuery to export GA4 data to BigQuery, and then GrowthBook can connect to GA4 data through BigQuery.

We'll detail the following steps:

1. Integrating the GrowthBook SDK on the client side
2. Configuring GA4 data export to BigQuery and setting up BigQuery connection in GrowthBook
3. Creating feature flags and handling them on the corresponding client
4. Creating experiments in GrowthBook
5. Analyzing experiment results

#### 1. Integrating the GrowthBook SDK on the Client Side

Here we use GrowthBook's JavaScript SDK. You can find detailed integration documentation in the [GrowthBook official documentation](https://docs.growthbook.io/lib/js).

```bash
npm install @growthbook/growthbook
```

Then we can integrate the GrowthBook SDK on the client side. Here we use the autoAttributesPlugin, which can automatically add user attributes (such as user ID, OS, browser, device, etc.) to the experiment.

```javascript
import { GrowthBook } from '@growthbook/growthbook';
import { autoAttributesPlugin } from "@growthbook/growthbook/plugins";

const growthbook = new GrowthBook({
  apiHost: "https://cdn.growthbook.io",
  clientKey: "sdk-abc123",
  plugins: [
    autoAttributesPlugin({}),
  ],
  trackingCallback: (experiment, result) => {
    // Send data to GA4
    gtag('event', 'experiment_viewed', {
      experiment_id: experiment.key,
      variant_id: result.variationId
    });
  }
});
```

It's particularly important to pay attention to the attribute settings, as GrowthBook determines whether users are included in experiments based on attributes. When we first integrated, we encountered abnormal experiment data due to incorrect attribute settings.

#### 2. Configuring GA4 Data Export to BigQuery and Setting Up BigQuery Connection in GrowthBook

This step requires you to have GA4 management permissions and have activated GCP services. The official documentation [A/B Testing with Google Analytics 4 (GA4) and GrowthBook](https://docs.growthbook.io/guide/GA4-google-analytics) details how to configure GA4 data export to BigQuery, so I won't repeat it here.

Based on this, I'll add two important notes:

1. When configuring data export in GA4, you can select Streaming for the export frequency, which allows you to see data as close to real-time as possible.
2. When configuring the BigQuery connection in GrowthBook, the Default Dataset needs to be filled in correctly at once, otherwise other analysis functions may report errors and won't be able to automatically generate some common configuration items (such as Fact Tables, Metrics, etc.).

#### 3. Creating Feature Flags and Handling Them on the Corresponding Client

Common A/B tests typically involve control and experimental groups, and the specific implementation method is to use feature flags or feature toggles. In GrowthBook, it provides various types of feature flags, such as boolean, numeric, string, and JSON types. In most cases, we only need to use boolean types, which are the switches we commonly refer to. This is very convenient for controlling whether new features are enabled when we launch them, making it an ideal time for A/B testing.

Image from [here](https://docs.growthbook.io/features/basics)
![Feature Flag Creation](/media/17409248371440/20250302215340.png)  

Then, handle it on the client side, for example:

```javascript
// Experiment example
if (growthbook.isOn("new-feature")) {
  // Show new feature
} else {
  // Show original feature
}
```

#### 4. Creating an Experiment in GrowthBook

When creating an AA experiment in GrowthBook, you need to select the corresponding feature flag and set experiment variables. Taking the homepage button experiment as an example, we need to add reporting for button click events. We specifically choose an AA experiment (rather than an AB experiment) because when first integrating GrowthBook and GA4, we need to verify that the configuration is correct.

> Fun fact: An AA test is a special form of A/B testing where users are randomly divided into two groups, but both groups see exactly the same version. If the system is configured correctly, the data from both groups should be very similar. If there are significant differences in the data, it indicates that there may be configuration issues that need to be checked.

Image from [here](https://docs.growthbook.io/feature-flag-experiments)

![Experiment Creation](/media/17409248371440/20250302215449.png)  

Then click start experiment, and GA4 will begin collecting data.

You can confirm in BigQuery whether the data is being collected normally.

```sql
select * 
from `analytics_[your_property_id].events_intraday_*`
where event_name = 'experiment_viewed'
order by event_timestamp desc
limit 10;
```

Please replace `[your_property_id]` with your GA4 property id. If you can see the data, and the event_params include experiment_id and variant_id, where experiment_id is the key of our experiment and variant_id is the variable of our experiment, then the data is being collected normally.

![BigQuery Data](/media/17409248371440/20250302215216.png)  

#### 5. Analyzing Results

Finally, we can view and analyze the experiment results in GrowthBook.

Here we can select the click rate of the click event that we set up before the experiment started as the Goal Metrics, which is the target metric we want to achieve, and then randomly select a metric as Secondary Metrics, such as Sessions per User or Pages per Session.

![Experiment Configuration Metrics](/media/17409248371440/20250302214902.png)

It's also worth noting that GrowthBook provides two statistical analysis engines: Frequentist and Bayesian. The industry generally uses Frequentist, which uses p-values to determine whether experiment results are significant.

![Statistical Analysis Engine Configuration](/media/17409248371440/20250302215048.png)  

Finally, click the Update button, and we can see the results of the experiment (image from [here](https://docs.growthbook.io/app/experiment-results#frequentist-engine)).

![Experiment Results](/media/17409248371440/20250302215741.png)  

### Conclusion

By combining GrowthBook and GA4, we can build a powerful A/B testing system. The key is to pay attention to every aspect of experiment design, data collection, and result analysis to ensure the scientific validity and effectiveness of the tests.

### P.S.

Why choose GrowthBook over other A/B testing tools?

- Our company currently uses GA4 as the main statistical analysis tool, so choosing GrowthBook makes it easier for us to integrate with GA4.
- GrowthBook's SDK is open source, and even its Cloud version is not expensive. You can find it on [GitHub](https://github.com/growthbook/growthbook). If you have customization needs in the future, you can modify it yourself.
- A/B testing methodology is already very mature, and there are many other tools available. Currently, GrowthBook meets our current needs, so there's no need to spend more time experimenting with other tools. 