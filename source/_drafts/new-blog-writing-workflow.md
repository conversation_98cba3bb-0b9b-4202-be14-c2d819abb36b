---
title: 我的博客新流程
author: xizhibei
tags:
---

很久之前，我在 [自动化你的 Hexo 博客创作流程](https://github.com/xizhibei/blog/issues/67) 中，介绍了我的博客创作流程，如今已 5 年有余，我也尝试用新的流程来进行写作。

其实就是返璞归真了，用 Hexo 本身提供的方式来写，编辑器用 VSCode 之类能够支持 Markdown 的编辑器就行。至于部署方式，那就更简单了，GitHub Pages 还是一如既往的好用，如今配合 GitHub Workflow，非常丝滑。

之前的流程：

1. 在 GitHub Issues 中写作
2. 用 [github-issues-to-hexo](https://github.com/xizhibei/github-issues-to-hexo) 把内容下载到本地
3. Git 提交，Push 到 GitHub
4. CI 运行，生成静态网页，部署至 GitHub Pages

这个流程有个非常蛋疼的点，那就是图片，如果有文章需要配图的话，就需要先提交到 GitHub 生成了连接，再在本地替换好，最后发布至 GitHub Issues，可谓非常繁琐，这也是之前我很少写配图文章的原因。

现在的流程：

1. 在 VSCode 中写作
2. Git 提交，Push 到 GitHub
3. CI 运行，生成静态网页，部署至 GitHub Pages

可以看到，少了一个繁琐的步骤，却省了很多的麻烦事，并且配图也很方便，本地也能实时预览。

并且，现在用到了 GitHub Workflow，也是简单好用，如果感兴趣的话，可以看 [这里](https://github.com/xizhibei/blog/blob/master/.github/workflows/main.yml)