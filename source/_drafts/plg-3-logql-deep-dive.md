---
title: 【PLG 系列】（三）LogQL 深度解析：从基础查询到高级分析
date: 2025-02-04 14:20:15
categories: [可观测性工程]
tags: [PLG Stack, 日志查询, Loki, LogQL, 日志分析]
author: xizhibei
---

### 前言

在[部署实战](/zh-cn/2024/08/22/plg-2-deployment-from-config-to-query/)中我们体验了基础查询，本文将深入解析 LogQL 的完整能力体系。作为 Loki 的查询语言，LogQL 融合了 PromQL 的指标分析能力和日志处理特性，最新版本 3.1 已支持 87 个内置函数<sup>1</sup>。

<!-- more -->

### 基础查询强化

#### 1. 标签过滤运算符
```logql
{container=~"loki-test-.+", namespace!="default"} 
  |= "error" 
  != "connection reset"
```
- `=~` 正则匹配容器名
- `!=` 排除特定错误类型

#### 2. 时间范围选择
```logql
{job="nginx"} 
  | json 
  | status >= 500 
  | rate(5m) > 0.1
```
时间窗口参数支持：
- 固定时长 `[5m]`
- 动态范围 `[now-1h]`
- 精确时间 `[2025-02-04T13:00:00Z, 2025-02-04T14:00:00Z]`

### 解析器实战

#### 1. 自动格式解析
```logql
{container="loki-test-flog-1"} 
  | logfmt 
  | remote_addr="192.168.1.*" 
  | duration > 500ms
```
![logfmt解析结果](media/17205193921030/17242966419959.jpg)

#### 2. JSON 深度处理
```logql
{app="order-service"} 
  | json 
  | line_format `{{.user_id}} {{.amount}}` 
  | label_format amount="{{.amount | printf "%.2f"}}"
```
- `line_format` 重构日志行
- `label_format` 格式化数值

### 指标聚合分析

#### 1. 错误率统计
```logql
sum by (status) (
  count_over_time(
    {namespace="prod"} 
    | json 
    | __error__="" 
    [15m]
  )
) / 900
```

#### 2. 流量趋势预测
```logql
predict_linear(
  sum(rate({job="api-gateway"}[5m]))[1h:1m],
  300
)
```

### 性能优化实践

1. **查询加速**  
添加 `| line_format` 前先过滤：
```logql
{container="frontend"} 
  |~ "render_time" 
  | json 
  | render_time > 1s
```

2. **存储优化**  
在 loki-config.yaml 启用分块索引：
```yaml
schema_config:
  configs:
    - from: 2025-01-01
      index:
        period: 24h
        prefix: loki_index_
```

### 典型场景案例

#### 1. 异常登录检测
```logql
{app="auth-service"} 
  | logfmt 
  | status="failed" 
  | rate(1h) by (user) > 5
```

#### 2. 接口性能分析
```logql
avg(
  {app="payment-service"}
  | json
  | __error__=""
  | latency 
) by (endpoint)
```

### 总结

本文详细解析了 LogQL 的核心功能，涵盖标签过滤、日志解析、指标聚合等关键领域。通过实际案例展示了如何将 LogQL 应用于生产环境监控，后续文章将深入探讨 Loki 的存储架构优化策略。

### Ref
1. [LogQL 官方文档](https://grafana.com/docs/loki/latest/query/)
