---
title: Nestjs - WebSocket 集成实践指南
tags: [Nestjs, WebSocket, Socket.io]
---

现代 Web 应用对实时通信的需求日益增长，WebSocket 作为双向通信协议成为重要解决方案。NestJS 通过封装 Socket.io 提供了优雅的 WebSocket 实现方式，本文将介绍如何基于 NestJS 构建实时应用。([1](https://docs.nestjs.com/websockets/gateways))

## 环境准备与项目创建

### 初始化 NestJS 项目

使用 Nest CLI 快速搭建项目基础结构：

```bash
$ npm i -g @nestjs/cli
$ nest new websocket-demo
```

选择包管理器后等待依赖安装完成，这是官方推荐的项目创建方式。([2](https://docs.nestjs.com/first-steps))

### 安装 WebSocket 依赖

```bash
$ npm install @nestjs/websockets socket.io
```

该依赖包含 WebSocket 网关实现和 Socket.io 客户端库。

## WebSocket 网关实现

### 创建基础网关

```typescript
import { WebSocketGateway, SubscribeMessage } from '@nestjs/websockets';

@WebSocketGateway({
  namespace: 'chat',
  transports: ['websocket']
})
export class ChatGateway implements OnGatewayInit {
  afterInit(server: Server) {
    console.log('WebSocket server initialized');
  }

  @SubscribeMessage('message')
  handleMessage(client: any, payload: any): string {
    return 'Message received';
  }
}
```

网关类使用 `@WebSocketGateway` 装饰器声明，`@SubscribeMessage` 处理特定消息事件。([3](https://docs.nestjs.com/websockets/gateways#gateways))

### 配置端口与中间件
```typescript
@WebSocketGateway({
  cors: {
    origin: '*',
  },
  transports: ['websocket']
})
```

可通过配置对象设置 CORS、传输协议等参数，建议生产环境限制 origin。

### 网关配置进阶

#### 异步初始化配置
```typescript
@WebSocketGateway({
  namespace: 'chat',
  transports: ['websocket']
})
export class ChatGateway implements OnGatewayInit {
  afterInit(server: Server) {
    console.log('WebSocket server initialized');
  }
}
```

通过实现 `OnGatewayInit` 接口的 `afterInit` 方法，可在网关初始化完成后执行异步操作<sup>[6]</sup>

#### 命名空间配置

```typescript
@WebSocketGateway(3001, { 
  namespace: 'notifications',
  path: '/ws/notifications'
})
```

支持多命名空间隔离不同业务场景的通信，path 参数指定 WebSocket 端点路径<sup>[1]</sup>

## 客户端连接管理

### 连接生命周期处理

```typescript
import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';

@WebSocketGateway()
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  
  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }
}
```

实现接口可监听连接/断开事件，适合记录日志或初始化会话。([4](https://docs.nestjs.com/websockets/gateways#lifecycle-hooks))

### 房间管理示例

```typescript
@SubscribeMessage('joinRoom')
handleJoinRoom(client: Socket, room: string): void {
  client.join(room);
  this.server.to(room).emit('userJoined', client.id);
}
```

使用 Socket.io 的房间功能实现分组通信，`this.server` 为底层 Socket.io 实例。

## 进阶功能实现

### 身份验证守卫

```typescript
import { WsGuard } from './auth/ws.guard';

@UseGuards(WsGuard)
@WebSocketGateway()
export class SecureGateway {
  // ...
}
```

通过自定义守卫实现 WebSocket 连接鉴权，需配合 JWT 等认证方案。([5](https://docs.nestjs.com/websockets/guards))

### 消息验证管道

```typescript
@SubscribeMessage('createMessage')
@UsePipes(new ValidationPipe())
async createMessage(
  @MessageBody() createDto: CreateMessageDto
) {
  // 处理已验证的数据
}
```

使用 class-validator 确保消息负载的完整性，提升系统健壮性。

### Redis 适配器配置

```typescript
import { RedisIoAdapter } from './adapters/redis-adapter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useWebSocketAdapter(new RedisIoAdapter(app));
}
```

多实例部署时需使用 Redis 适配器实现跨实例通信<sup>[7]</sup>

### 自定义适配器

```typescript
import { WebSocketAdapter } from '@nestjs/common';

export class CustomAdapter implements WebSocketAdapter {
  create(port: number, options: any) {
    return new WebSocket.Server({ port, ...options });
  }
  // 实现其他接口方法
}

```

通过实现 `WebSocketAdapter` 接口可集成其他 WebSocket 库（如 ws）<sup>[7]</sup>

### 消息确认机制

```typescript
@SubscribeMessage('directMessage')
async handleDirectMessage(
  @ConnectedSocket() client: Socket,
  @MessageBody() data: { to: string; content: string }
): Promise<WsResponse<string>> {
  const result = await this.chatService.sendMessage(data);
  return { event: 'messageAck', data: result };
}
```

通过返回 `WsResponse` 对象实现服务端消息确认机制<sup>[6]</sup>

## 部署注意事项

1. 负载均衡场景需启用粘性会话
2. 生产环境应配置合理的 ping/pong 超时
3. 建议结合 Redis 适配器实现多实例通信
4. 监控连接数和消息吞吐量指标

## 参考资料

1. [NestJS 官方文档 - WebSockets](https://docs.nestjs.com/websockets/gateways)
2. [NestJS 快速入门指南](https://docs.nestjs.com/first-steps)
3. [Socket.io 官方文档](https://socket.io/docs/v4/)
6. [NestJS 网关生命周期](https://docs.nestjs.com/websockets/gateways#lifecycle-hooks)
7. [WebSocket 适配器文档](https://docs.nestjs.com/websockets/adapter)

