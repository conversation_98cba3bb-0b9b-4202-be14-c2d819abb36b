在 NestJS 中，序列化是一个非常重要的概念，它决定了如何将对象转换为字节流，以及如何将字节流转换为对象。在多数情况下，你可能不需要自定义序列化，因为 NestJS 已经提供了默认的序列化方式。但是，在实际使用中，我们可能需要自定义序列化方式，比如使用压缩算法，或者使用某种加密算法，或者需要跟一些硬件设备进行通信，这时候我们就需要自定义序列化方式。

这里的特性，在官方的文档中并没有明确说明，但是这些特性在实际使用中非常有用，为了说清楚这些特性，我们需要阅读源码。需要提前说清楚的是，阅读源码本身是一件很简单且自然的事情，不要觉得它很困难的事情，更不要觉得它是一件很高级的事情。对于今天的内容，我们可以先从参数开始，搞清楚它们是如何被使用的，一般来说我们可以从源码中找到答案，也就是如何创建自己的序列化器和反序列化器。

```typescript
// https://github.com/nestjs/nest/blob/v11.0.10/packages/microservices/interfaces/microservice-configuration.interface.ts#L99
export interface TcpOptions {
  transport?: Transport.TCP;
  options?: {
    host?: string;
    port?: number;
    retryAttempts?: number;
    retryDelay?: number;
    serializer?: Serializer;
    tlsOptions?: TlsOptions;
    deserializer?: Deserializer;
    socketClass?: Type<TcpSocket>;
  };
}
```

从这个参数我们能看出，序列化器和反序列化器是 `TcpOptions` 的参数，它们分别是 `Serializer` 和 `Deserializer` 类型。为了进一步确认，我们可以看一看它是在哪里被使用的。通过简单的搜索就能找到：TcpServer。它就在 `@nestjs/microservices` 的源码中，具体就在这里：[`ServerTCP`](https://github.com/nestjs/nest/blob/v11.0.10/packages/microservices/server/server-tcp.ts)。


```typescript
// https://github.com/nestjs/nest/blob/v11.0.10/packages/microservices/server/server-tcp.ts#L44

constructor(private readonly options: Required<TcpOptions>['options']) {
    super();
    this.port = this.getOptionsProp(options, 'port', TCP_DEFAULT_PORT);
    this.host = this.getOptionsProp(options, 'host', TCP_DEFAULT_HOST);
    this.socketClass = this.getOptionsProp(options, 'socketClass', JsonSocket);
    this.tlsOptions = this.getOptionsProp(options, 'tlsOptions');

    this.init();
    this.initializeSerializer(options);
    this.initializeDeserializer(options);
}
```

然后，我们来看它们是在构造器中如何被使用的，看起来是 `this.initializeSerializer(options)` 和 `this.initializeDeserializer(options)`，ServerTCP 中并没有发现它们的实现，但是我们可以通过搜索发现，它们在 `ServerTCP` 的父类 `Server` 中被实现：[`Server`](https://github.com/nestjs/nest/blob/v11.0.10/packages/microservices/server/server.ts)。

```typescript
// https://github.com/nestjs/nest/blob/v11.0.10/packages/microservices/server/server.ts#L241C3-L265C4
protected initializeSerializer(options: ClientOptions['options']) {
    this.serializer =
    (options &&
    (options as
        | RedisOptions['options']
        | NatsOptions['options']
        | MqttOptions['options']
        | TcpOptions['options']
        | RmqOptions['options']
        | KafkaOptions['options'])!.serializer) ||
    new IdentitySerializer();
}

protected initializeDeserializer(options: ClientOptions['options']) {
    this.deserializer =
    (options! &&
    (options as
        | RedisOptions['options']
        | NatsOptions['options']
        | MqttOptions['options']
        | TcpOptions['options']
        | RmqOptions['options']
        | KafkaOptions['options'])!.deserializer) ||
    new IncomingRequestDeserializer();
}
```

好了，我们发现了关键，当没有参数传入时，会使用默认的序列化器和反序列化器，也就是 `IdentitySerializer` 和 `IncomingRequestDeserializer`。

然后，我们再来看 `IdentitySerializer` 和 `IncomingRequestDeserializer` 的实现：

```typescript
// https://github.com/nestjs/nest/blob/v11.0.10/packages/microservices/serializers/identity.serializer.ts
export class IdentitySerializer implements Serializer {
  serialize(value: any) {
    return value;
  }
}
```

```typescript
// https://github.com/nestjs/nest/blob/v11.0.10/packages/microservices/deserializers/incoming-request.deserializer.ts
export class IncomingRequestDeserializer implements ConsumerDeserializer {
  deserialize(
    value: any,
    options?: Record<string, any>,
  ): IncomingRequest | IncomingEvent {
    return this.isExternal(value) ? this.mapToSchema(value, options) : value;
  }

  // ...
}
```

似乎，我们发现了什么，`IdentitySerializer` 和 `IncomingRequestDeserializer` 的实现非常简单，它们只是做了很简单的处理，甚至都没有 JSON 相关的编解码。


