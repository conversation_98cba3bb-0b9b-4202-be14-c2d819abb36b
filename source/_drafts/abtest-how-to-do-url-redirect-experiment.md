---
title: 如何使用 GrowthBook 进行 URL 跳转实验
date: 2024-03-21
tags: [GrowthBook, A/B Testing, URL Redirect]
---

在网站优化和用户体验改进的过程中，URL 跳转实验是一个重要的工具。通过 GrowthBook，我们可以轻松地实现 URL 跳转实验，并且支持 SPA（单页应用）和 SSR（服务端渲染）两种不同的场景。本文将详细介绍如何在这两种场景下实现 URL 跳转实验。

### 什么是 URL 跳转实验

URL 跳转实验是一种 A/B 测试方法，通过将用户重定向到不同的 URL 来测试不同的页面版本。这种实验通常用于测试：

- 新旧页面版本的对比
- 不同营销页面的效果
- 不同产品页面的转化率

### SPA 场景下的实现方案

在单页应用中，我们可以使用 GrowthBook 的客户端 SDK 来实现 URL 跳转。以下是具体实现步骤：

1. 首先安装 GrowthBook 客户端 SDK：

```bash
npm install @growthbook/growthbook-react
```

2. 在应用入口文件中初始化 GrowthBook：

```javascript
import { GrowthBook, GrowthBookProvider } from "@growthbook/growthbook-react";

// 创建 GrowthBook 实例
const growthbook = new GrowthBook({
  apiHost: process.env.NEXT_PUBLIC_GROWTHBOOK_API_HOST,
  clientKey: process.env.NEXT_PUBLIC_GROWTHBOOK_CLIENT_KEY,
  enableDevMode: true,
  // 设置用户属性，用于实验分组
  attributes: {
    id: "user123",
    country: "CN",
    device: "desktop"
  }
});

function App() {
  useEffect(() => {
    // 加载特性标志
    growthbook.loadFeatures();
  }, []);

  return (
    <GrowthBookProvider growthbook={growthbook}>
      <Router>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/experiment" element={<ExperimentPage />} />
        </Routes>
      </Router>
    </GrowthBookProvider>
  );
}
```

3. 在需要跳转的组件中使用 GrowthBook 进行判断：

```javascript
import { useFeatureValue } from "@growthbook/growthbook-react";

function ExperimentPage() {
  // 获取重定向 URL，如果实验未开启则返回默认路径
  const redirectUrl = useFeatureValue("redirect-url", "/default-path");
  
  useEffect(() => {
    if (redirectUrl !== "/default-path") {
      // 记录实验曝光
      growthbook.track("experiment_viewed", {
        experimentId: "redirect-url",
        variation: redirectUrl
      });
      
      // 执行重定向
      window.location.href = redirectUrl;
    }
  }, [redirectUrl]);

  return <div>Loading...</div>;
}
```

4. 在 GrowthBook 控制台中配置实验：

```javascript
{
  "id": "redirect-url",
  "variations": [
    {
      "key": "control",
      "value": "/default-path"
    },
    {
      "key": "variation1",
      "value": "/new-landing-page"
    },
    {
      "key": "variation2",
      "value": "/alternative-page"
    }
  ],
  "weights": [0.33, 0.33, 0.34],
  "targeting": {
    "country": ["CN", "US"],
    "device": ["desktop", "mobile"]
  }
}
```

### SSR 场景下的实现方案

在服务端渲染的场景下，我们需要使用 GrowthBook 的服务端 SDK 来实现 URL 跳转。以下是具体实现步骤：

1. 安装服务端 SDK：

```bash
npm install @growthbook/growthbook
```

2. 创建服务端 GrowthBook 工具函数：

```javascript
// utils/growthbook.js
import { GrowthBook } from "@growthbook/growthbook";

export async function getGrowthBookInstance(req) {
  const growthbook = new GrowthBook({
    apiHost: process.env.GROWTHBOOK_API_HOST,
    clientKey: process.env.GROWTHBOOK_CLIENT_KEY,
    // 从请求中获取用户属性
    attributes: {
      id: req.cookies.userId,
      country: req.headers["cf-ipcountry"],
      device: req.headers["user-agent"].includes("Mobile") ? "mobile" : "desktop"
    }
  });

  await growthbook.loadFeatures();
  return growthbook;
}
```

3. 在页面组件中使用 GrowthBook：

```javascript
// pages/experiment.js
import { getGrowthBookInstance } from "../utils/growthbook";

export async function getServerSideProps({ req, res }) {
  const growthbook = await getGrowthBookInstance(req);
  
  // 获取重定向 URL
  const redirectUrl = growthbook.getFeatureValue("redirect-url", "/default-path");

  if (redirectUrl !== "/default-path") {
    // 记录实验曝光
    growthbook.track("experiment_viewed", {
      experimentId: "redirect-url",
      variation: redirectUrl
    });

    // 使用 302 重定向
    res.writeHead(302, {
      Location: redirectUrl,
      "Cache-Control": "no-cache, no-store, must-revalidate"
    });
    res.end();
    return { props: {} };
  }

  return {
    props: {
      // 传递实验数据到客户端
      growthbookData: growthbook.getData()
    }
  };
}

// 客户端组件
function ExperimentPage({ growthbookData }) {
  return (
    <div>
      <h1>实验页面</h1>
      <p>当前实验状态：{growthbookData.features["redirect-url"].value}</p>
    </div>
  );
}
```

4. 在 Next.js 中间件中实现全局重定向：

```javascript
// middleware.js
import { NextResponse } from "next/server";
import { GrowthBook } from "@growthbook/growthbook";

export async function middleware(request) {
  const growthbook = new GrowthBook({
    apiHost: process.env.GROWTHBOOK_API_HOST,
    clientKey: process.env.GROWTHBOOK_CLIENT_KEY,
    attributes: {
      id: request.cookies.get("userId"),
      path: request.nextUrl.pathname
    }
  });

  await growthbook.loadFeatures();
  
  const redirectUrl = growthbook.getFeatureValue("redirect-url", null);
  
  if (redirectUrl) {
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  return NextResponse.next();
}

// 配置需要应用中间件的路径
export const config = {
  matcher: ["/experiment/:path*"]
};
```

### 最佳实践

1. **设置合适的实验周期**：URL 跳转实验通常需要较长的实验周期，建议至少运行 2-4 周。

2. **监控关键指标**：在实验过程中，需要密切关注以下指标：
   - 跳转成功率
   - 目标页面的转化率
   - 用户停留时间

3. **设置合适的样本量**：根据统计显著性要求，确保实验有足够的样本量。

4. **渐进式发布**：建议采用渐进式发布策略，先从少量用户开始，逐步扩大实验范围。

### 注意事项

1. 在进行 URL 跳转实验时，需要确保：
   - 目标页面已经准备就绪
   - 跳转逻辑不会影响用户体验
   - 有合适的回滚机制

2. 对于 SEO 敏感的场景，建议：
   - 使用 302 临时重定向而不是 301 永久重定向
   - 在 robots.txt 中适当配置爬虫规则

3. 在 SPA 场景下，需要注意：
   - 处理路由状态
   - 避免重复跳转
   - 考虑用户会话的连续性

### 参考资料

[^1]: [GrowthBook 官方文档](https://docs.growthbook.io/)
[^2]: [A/B 测试最佳实践](https://www.optimizely.com/optimization-glossary/ab-testing/)
[^3]: [URL 重定向对 SEO 的影响](https://developers.google.com/search/docs/crawling-indexing/301-redirects)
