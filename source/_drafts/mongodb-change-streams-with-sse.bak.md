---
title: MongoDB Change Stream 与 SSE 结合实现实时数据同步
tags: [MongoDB, Change Stream, SSE, 数据库, 实时数据, 数据同步, 最佳实践]
categories: [数据库,MongoDB]
---

最近在做多租户 SaaS 平台时，遇到一个挺有意思的需求：产品经理希望用户能在数据有变动时，第一时间收到通知。说实话，这种“实时推送”听起来很酷，但实现起来可没那么简单。本文就聊聊我在用 MongoDB Change Stream 搭配 SSE（Server-Sent Events）做实时数据同步时的踩坑和收获，希望能帮你少走弯路，也欢迎交流你的实践经验[^1]。

### 从需求到方案：为什么选 Change Stream + SSE？

事情的起因很简单。一天，产品经理找到我：“能不能让每个租户的用户在有数据变更时，马上收到消息？”我一听，这不就是实时通知嘛！但细聊下来，需求其实挺多：

- 不同租户之间要完全隔离，不能串消息
- 用户只关心自己订阅的那类变更
- 最好别太复杂，后期还能扩展

一开始我也想过用轮询，但那种方式太“土”了，延迟高、资源浪费。后来调研发现，MongoDB 的 Change Stream 能实时监听数据变更，SSE 又能让前端轻松接收推送，二者结合正好能满足需求。

> 你可以把最初的系统想象成一条消息流水线：数据库一有风吹草动，SSE 服务器就立刻“喊话”，每个租户的客户端都能第一时间收到消息。

```mermaid
graph LR
    A[MongoDB] -->|Change Stream| B[SSE Server]
    B -->|Filtered SSE| C[Tenant 1 Client]
    B -->|Filtered SSE| D[Tenant 2 Client]
    B -->|Filtered SSE| E[Tenant 3 Client]
```

### 实现第一步：最简单的实时推送

刚开始时，我只想先跑通最小可用版本。思路很直接：

1. 后端用 Change Stream 监听 MongoDB 的变更
2. 有变更就通过 SSE 推给前端
3. 每个租户的数据用 tenantId 隔离

代码实现其实不难，核心逻辑大致如下：

> 这里的关键点是：每个租户的数据都要隔离，不能串台。Change Stream 的 pipeline 就派上用场了。

```typescript
// change-stream.service.ts
@Injectable()
export class ChangeStreamService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
  ) {}

  async watchChanges(tenantId: string, filters?: any) {
    // 只监听本租户的数据
    const pipeline = [
      {
        $match: {
          'fullDocument.tenantId': tenantId,
          ...filters
        }
      }
    ];

    const changeStream = this.userModel.watch(pipeline, {
      fullDocument: 'updateLookup'
    });
    changeStream.on('change', (change) => {
      this.notifyClients(tenantId, change);
    });
    return changeStream;
  }
}

// sse.controller.ts
@Controller('events')
export class SSEController {
  private clients = new Map<string, Set<Response>>();

  @Sse('stream')
  streamEvents(
    @Headers('x-tenant-id') tenantId: string,
    @Query('filters') filters?: string
  ): Observable<MessageEvent> {
    if (!tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }
    return new Observable((subscriber) => {
      if (!this.clients.has(tenantId)) {
        this.clients.set(tenantId, new Set());
      }
      const tenantClients = this.clients.get(tenantId)!;
      tenantClients.add(subscriber);
      const parsedFilters = filters ? JSON.parse(filters) : {};
      const changeStream = this.changeStreamService.watchChanges(tenantId, parsedFilters);
      return () => {
        tenantClients.delete(subscriber);
        changeStream.close();
        if (tenantClients.size === 0) {
          this.clients.delete(tenantId);
        }
      };
    });
  }

  notifyClients(tenantId: string, change: any) {
    const tenantClients = this.clients.get(tenantId);
    if (!tenantClients) return;
    tenantClients.forEach((client) => {
      client.next({
        data: change,
        type: 'message',
        id: Date.now().toString(),
      });
    });
  }
}
```

#### 系统优缺点分析

优点：
- 架构简单，易于实现
- 实时性好，延迟低
- 支持多租户隔离
- 支持消息过滤
- 维护成本低
- 适合中小规模应用

缺点：
- 每个租户的 Change Stream 都会占用一个数据库连接
- 不支持消息持久化
- 缺乏消息重放机制
- 扩展性有限
- 不支持断线重连
- Change Stream 的 pipeline 过滤无法使用索引[^2]，可能导致性能问题：
  - 所有变更事件都需要经过 pipeline 处理
  - 过滤条件越复杂，性能影响越大
  - 在高并发场景下可能造成服务器压力

#### 性能优化建议

为了缓解 Change Stream pipeline 的性能问题，我们可以采取以下措施：

1. 简化过滤条件
   - 避免复杂的过滤逻辑
   - 优先使用简单的字段匹配
   - 减少正则表达式等复杂操作

2. 应用层过滤
   - 在应用层进行二次过滤
   - 将复杂的过滤逻辑从 pipeline 移到应用层
   - 利用应用层的缓存机制

3. 合理使用 Change Stream 选项
   - 使用 `fullDocument: 'updateLookup'` 时注意性能影响
   - 考虑使用 `fullDocument: 'whenAvailable'` 减少查询开销
   - 适当设置 `maxAwaitTimeMS` 控制轮询间隔

4. 监控和告警
   - 监控 Change Stream 的延迟
   - 设置 pipeline 处理时间的告警阈值
   - 定期检查 oplog 大小和状态

#### 使用示例

> 前端用 EventSource 连接 SSE，指定租户和过滤条件就行。这样每个用户都能收到属于自己的“专属推送”。

```javascript
const eventSource = new EventSource(
  '/events/stream?filters=' + encodeURIComponent(JSON.stringify({
    operationType: 'update',
    'fullDocument.status': 'active'
  })), {
    headers: {
      'x-tenant-id': 'tenant-123'
    }
  }
);

eventSource.onmessage = (event) => {
  const change = JSON.parse(event.data);
  console.log('Received change:', change);
};
```

这种方式上线很快，效果也不错。每个租户的数据都能隔离，消息也能按需推送。最初的用户反馈也挺好。

### 现实的挑战：性能和扩展性

> 但好景不长，随着租户和并发数增加，问题就来了。

最明显的就是数据库连接数暴涨——每个租户都要单独开一个 Change Stream，MongoDB 服务器压力很大。

> 这里有个细节很容易被忽略：Change Stream 的 pipeline 过滤，其实是在内存里做的，不能用索引[^2]。这意味着什么？一旦过滤条件复杂起来，MongoDB 的压力就会直线上升。我当时就遇到过，租户一多，服务器直接告急。后来才明白，设计过滤条件时一定要尽量简单，复杂逻辑最好放到应用层来处理。

还有，消息没法持久化，断线重连也不太友好。

这些坑，都是我踩过的。后来我才明白，架构设计真的是“用起来简单，做起来不易”。

### 进阶优化：引入消息队列和负载均衡

> 为了让系统更稳、更能抗压，我决定引入消息队列（比如 Redis Pub/Sub），再加上 Nginx 做负载均衡。你可以把它想象成一个“大水池”，所有变更消息先丢进池子里，后端服务慢慢消费，前端也能更平滑地收到推送。

```mermaid
graph LR
    A[MongoDB] -->|Change Stream| B[Message Queue]
    B -->|Consume| C[SSE Server 1]
    B -->|Consume| D[SSE Server 2]
    B -->|Consume| E[SSE Server 3]
    F[Nginx Load Balancer] -->|Hash Based| C
    F -->|Hash Based| D
    F -->|Hash Based| E
    C -->|SSE| G[Client Group 1]
    D -->|SSE| H[Client Group 2]
    E -->|SSE| I[Client Group 3]
```

这样做的好处很明显：
- 数据库连接数大大减少
- 可以水平扩展 SSE 服务器，抗并发能力提升
- 消息可以持久化，断线重连也更容易实现

> 下面是核心代码片段，Redis 负责消息分发，SSE 服务器负责推送。

```typescript
// message-queue.service.ts
@Injectable()
export class MessageQueueService {
  constructor(
    private readonly redisService: RedisService,
  ) {}

  async publishChange(change: any) {
    await this.redisService.publish('changes', JSON.stringify(change));
  }
}

// sse-server.service.ts
@Injectable()
export class SSEServerService {
  constructor(
    private readonly redisService: RedisService,
  ) {}

  async start() {
    const subscriber = this.redisService.createClient();
    await subscriber.subscribe('changes');
    
    subscriber.on('message', (channel, message) => {
      this.notifyClients(JSON.parse(message));
    });
  }
}
```

有了消息队列，系统的弹性和可维护性都提升了不少。Nginx 负责把客户端请求分发到不同的 SSE 服务器，哪台机器挂了也不怕，随时可以扩容。

### 企业级进阶：高可用与安全性

> 如果你做的是企业级应用，需求会更复杂，比如消息不能丢，要能重放，还要认证授权、监控、告警、日志、灾备……

这时候，Kafka、Elasticsearch、API 网关、Prometheus 等工具就都派上用场了。每个环节都能独立扩展，消息流转有追踪，安全和合规也能做到位。

> 架构图看起来就复杂多了，但每个环节都能独立扩展，系统也更健壮。

```mermaid
graph TD
    A[MongoDB] -->|Change Stream| B[Message Queue]
    B -->|Persist| C[Message Store]
    B -->|Consume| D[Message Processor]
    D -->|Route| E[SSE Cluster]
    E -->|Auth| F[API Gateway]
    F -->|SSE| G[Clients]
    H[Monitoring] -->|Metrics| I[Alert System]
    H -->|Logs| J[Log Aggregator]
    H -->|Traces| K[Distributed Tracing]
    L[Security] -->|Auth| M[Identity Service]
    L -->|Encrypt| N[Key Management]
    L -->|Audit| O[Audit Log]
```

> 比如，消息处理服务可以这样设计，既能持久化、路由，也能追踪消息流转。

```typescript
// message-processor.service.ts
@Injectable()
export class MessageProcessorService {
  constructor(
    private readonly kafkaService: KafkaService,
    private readonly elasticsearchService: ElasticsearchService,
  ) {}
  async processMessage(message: any) {
    await this.elasticsearchService.index('messages', message); // 持久化
    const route = this.determineRoute(message);
    await this.kafkaService.publish(route.topic, message); // 路由
    await this.traceMessage(message); // 追踪
  }
  private determineRoute(message: any) {
    return {
      topic: `tenant.${message.tenantId}.${message.type}`,
      partition: this.calculatePartition(message),
    };
  }
}
```

### 实际应用场景分享

在实际项目中，这套架构让我少踩了不少坑。比如做实时数据同步时，Change Stream + 消息队列 + SSE 的组合，既能保证实时性，也能灵活扩展。做通知系统时，用户级别的消息过滤和优先级管理也很容易实现。

当然，系统越复杂，维护和运维的挑战也越大。比如 Redis/Kafka 的监控、消息堆积、连接数管理、灾备切换等，都需要提前规划好。

### 小结与感悟

回头看，实时推送系统的架构演进，其实就是不断权衡和取舍的过程。最初的方案简单直接，适合小规模应用；后期引入消息队列和负载均衡，系统弹性和扩展性大大提升；企业级场景下，安全、合规、监控、灾备都要考虑进来。

我的经验是：没有银弹，只有适合当前业务的最佳实践。希望这篇文章能帮你少踩坑，也欢迎留言交流你的实践心得！

### 参考资料
[^1]: [MongoDB Change Streams Documentation](https://www.mongodb.com/docs/manual/changeStreams/)
[^2]: [Server-Sent Events Specification](https://html.spec.whatwg.org/multipage/server-sent-events.html)
[^3]: [NestJS SSE Documentation](https://docs.nestjs.com/techniques/server-sent-events)
[^4]: [Redis Pub/Sub Documentation](https://redis.io/topics/pubsub)
[^5]: [MongoDB Change Streams Production Recommendations](https://www.mongodb.com/docs/manual/administration/change-streams-production-recommendations/)


