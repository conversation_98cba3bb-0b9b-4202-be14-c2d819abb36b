---
title: 【NestJS 系列】TCP 微服务实践指南
tags: [NestJS, 微服务]
---

在构建现代分布式系统时，微服务架构已经成为一种主流选择。NestJS 作为一个渐进式的 Node.js 框架，提供了强大的微服务支持。本文将详细介绍如何使用 NestJS 构建 TCP 微服务，并分享一些实践经验。

### TCP 微服务的优势

在 NestJS 微服务生态中，TCP 传输层具有以下特点：

- 可靠的数据传输保证
- 适合需要保持连接的场景
- 相比 HTTP 具有更低的协议开销
- 支持全双工通信
- 灵活的协议扩展性（也就是说，你可以创建自己的协议）

### 快速开始

首先安装必要的依赖：

```bash
npm install @nestjs/microservices
```

#### 服务端配置

创建微服务的入口文件 `main.ts`：

```typescript
import { NestFactory } from '@nestjs/core';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.TCP,
      options: {
        host: '0.0.0.0',
        port: 3000,
        retryAttempts: 5,
        retryDelay: 3000
      },
    },
  );
  
  await app.listen();
  console.log('Microservice is listening');
}
bootstrap();
```

### 通信模式实现

NestJS 提供了两种主要的消息模式：

#### 1. 请求-响应模式

这种模式适合需要等待响应的场景：

```typescript
// 服务端
@Controller()
export class MathController {
  @MessagePattern({ cmd: 'calculate' })
  async calculate(data: { numbers: number[], operation: string }): Promise<number> {
    const { numbers, operation } = data;
    switch(operation) {
      case 'sum':
        return numbers.reduce((a, b) => a + b, 0);
      case 'multiply':
        return numbers.reduce((a, b) => a * b, 1);
      default:
        throw new RpcException('Operation not supported');
    }
  }
}
```

#### 2. 事件模式

适用于发布-订阅场景：

```typescript
// 服务端
@Controller()
export class LoggerController {
  @EventPattern('log_event')
  async handleLogEvent(data: { level: string; message: string }) {
    const { level, message } = data;
    console.log(`[${level.toUpperCase()}]: ${message}`);
  }
}
```

### 高级特性

#### 1. 自定义序列化

```typescript
import { Serializer } from '@nestjs/microservices';
import { compress, decompress } from 'some-compression-lib';

export class CompressedSerializer implements Serializer {
  serialize(value: any): Buffer {
    const jsonString = JSON.stringify(value);
    return compress(Buffer.from(jsonString));
  }

  deserialize(value: Buffer): any {
    const decompressed = decompress(value);
    return JSON.parse(decompressed.toString());
  }
}
```

#### 2. 健康检查实现

```typescript
@Controller('health')
export class HealthController {
  @MessagePattern({ cmd: 'health_check' })
  async check() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        database: 'up',
        cache: 'up'
      }
    };
  }
}
```
