---
title: 【TAPD 系列】（一）介绍
tags: [TAPD,教程]
---

TAPD 已在我们公司使用了快 3 年了，前不久刚收到了它的通知，小微扶持计划又给我们续命了，还是挺感激的，毕竟我们小公司，在这个经济下行的时间里，能省则省了，不过还是希望之后有能力给它交钱，现在的话，就给它做点宣传了。

其实我在整个职业生涯中，除了 TAPD 之外用过不少的项目管理软件，从 Tower 到 Teambition、 Jira 等。虽说这些软件大同小异，但是从个人体验来说，TAPD 会更让我有掌控感一些，不过在一开始上手的时间里面，它还有不少难度的，从易用这个角度来说，它的成本会高一些。

我也是通过不断的摸索，在实践中，逐渐搞懂了它的用法。可以看到，TAPD 官网提供了不少的资料来帮助你[快速入门](https://www.tapd.cn/help)，但是我认为它的内容仅仅停留在告诉它有哪些功能，你可以怎么使用这个工具，却没有告诉你，你该如何使用好这个工具，简单来说，它**缺少实践教程**。因此我打算写一系列的文章来讲讲我是如何使用它的，它本身是个不错的工具，只是在缺少方法论的指引下，非常容易“用不起来”，其实工具本身没有好坏，只看你如何使用。

TAPD 本身确实有上手难度，但是我觉得它的难度更多体现在需要一个良好的研发流程作为基础，工具是用来配合流程的，如果你们本身的研发流程有问题，它并不会给你带来多大的提升。所以，当我在这里说 TAPD 的时候，其实我实际说的是研发流程。

### TAPD 的简单上手

在继续我的教程之前，还是有必要给没有使用过的同学介绍这个工具的（凑——篇——幅）。



### 后续规划

- 工作流制定
- 故事与任务
- 故事墙
- 迭代
- 统计图表、报表
- 缺陷
- 一个完整的产品功能迭代实践


[腾讯 TAPD DevOps 开放生态最佳实践](https://zhuanlan.zhihu.com/p/337410433)
[腾讯TAPD：系统设计全流程解构](https://www.woshipm.com/pd/4721119.html)
[TAPD 敏捷项目管理](https://cloud.tencent.com/document/product/624)
[研发效能提升 36 计第三课：束水攻沙，持续加快产品交付速度](https://developer.aliyun.com/article/719255)