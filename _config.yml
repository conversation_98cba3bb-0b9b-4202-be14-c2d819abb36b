# Hexo Configuration
## Docs: https://hexo.io/docs/configuration.html
## Source: https://github.com/hexojs/hexo/

# Site
title: 须臾之学
subtitle: 专注技术实践，沉淀认知体系
description: 分享 Node.js/Golang/Python/DevOps 实战经验，探讨系统设计原理，记录技术成长轨迹。原创技术文章、开源项目维护心得、全栈开发最佳实践。
author: 习之北 (@xizhibei)
language:
  - zh-cn
  - en
timezone: Asia/Shanghai

# URL
## If your site is put in a subdirectory, set url as 'http://yoursite.com/child' and root as '/child/'
url: https://blog.xizhibei.me
root: /
permalink: :lang/:year/:month/:day/:title/
permalink_defaults:
  lang: zh-cn

# Directory
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# Writing
new_post_name: :lang/:title.md # File name of new posts
default_layout: post
titlecase: false # Transform title into titlecase
external_link:
  enable: true # Open external links in new tab
filename_case: 0
render_drafts: false
post_asset_folder: false
relative_link: false
future: true
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace:

# Category & Tag
default_category: uncategorized
category_map:
tag_map:

# Date / Time format
## Hexo uses Moment.js to parse and display date
## You can customize the date format as defined in
## http://momentjs.com/docs/#/displaying/format/
date_format: YYYY-MM-DD
time_format: HH:mm:ss

# Pagination
## Set per_page to 0 to disable pagination
per_page: 15
pagination_dir: page

markdown:
  render:
    html: true
    xhtmlOut: false
    breaks: true
    linkify: true
    typographer: true
    quotes: '“”‘’'
  plugins:
    - markdown-it-footnote

# Extensions
## Plugins: https://hexo.io/plugins/
## Themes: https://hexo.io/themes/
theme: maupassant

theme_config:
  links:
  timeline:
  google_analytics:
  shareto: true
  wordcount: true
  disqus:
    enable: true
    shortname: xizhibei
    # api: https://disqus.skk.moe/disqus/
    apikey: ****************************************************************
  post_copyright:
    enable: false
    author: xizhibei
    copyright_text: 本文采用署名 - 非商业性使用 - 相同方式共享（BY-NC-SA）进行许可。
  menu:
    - page: home
      directory: .
      icon: fa-home
    - page: archive
      directory: archives/
      icon: fa-archive
    - page: about
      directory: about/
      icon: fa-user
  widgets:
    - search
    - tag
    - recent_posts
    - recent_comments
  info:
    avatar: /media/misc/avatar.png
    discription: To be a better man.
    outlinkitem:
  donate:
    enable: true ## If you want to display the donate button after each post, please set the value to true and fill the following items on your need. You can also enable donate button in a page by adding a "donate: true" item to the front-matter.
    github: ## GitHub URL, e.g. https://github.com/Kaiyuan/donate-page
    alipay_qr: /media/misc/alipayqr.jpg ## Path of Alipay QRcode image, e.g. /img/AliPayQR.png
    wechat_qr: /media/misc/wechatqr.jpg ## Path of Wechat QRcode image, e.g. /img/WeChatQR.png
    btc_qr: ## Path of Bitcoin QRcode image, e.g. /img/BTCQR.png
    btc_key: ## Bitcoin key, e.g. **********************************
    paypal_url: ## Paypal URL, e.g. https://www.paypal.me/tufu9441

  # mermaid chart
  mermaid: ## mermaid url https://github.com/knsv/mermaid
    enable: true
    version: 10.9.1
    options: {}