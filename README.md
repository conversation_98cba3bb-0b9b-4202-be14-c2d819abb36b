# Blog

我的博客，随便写写。

## 系列

### CMake

#### 中文

- [【CMake 系列】（一）入门](https://blog.xizhibei.me/zh-cn/2020/03/09/cmake-1-introduction/)
- [【CMake 系列】（二）第三方依赖管理](https://blog.xizhibei.me/zh-cn/2020/03/15/cmake-2-third-party-dependances-management/)
- [【CMake 系列】（三）ExternalProject 实践](https://blog.xizhibei.me/zh-cn/2020/03/23/cmake-3-external-project-practise/)
- [【CMake 系列】（四）用 GoogleTest 测试](https://blog.xizhibei.me/zh-cn/2020/04/05/cmake-4-test-with-google-test/)
- [【CMake 系列】（五）安装、打包与导出](https://blog.xizhibei.me/zh-cn/2020/04/20/cmake-5-install-package-and-export/)
- [【CMake 系列】（六）用 Doxygen、Sphinx 与 Breathe 创建文档](https://blog.xizhibei.me/zh-cn/2020/05/19/cmake-6-docs-with-doxygen-sphinx-breathe/)
- [【CMake 系列】（七）常用变量、函数以及模块](https://blog.xizhibei.me/zh-cn/2020/06/02/cmake-7-common-var-func-and-modules/)
- [【CMake 系列】（八）交叉编译](https://blog.xizhibei.me/zh-cn/2020/06/15/cmake-8-cross-compiling/)
- [【CMake 系列】（九）实战之如何下载依赖](https://blog.xizhibei.me/zh-cn/2020/06/30/cmake-9-implement-download-extract-file/)
- [【CMake 系列】（十）编译速度以及程序性能优化相关](https://blog.xizhibei.me/zh-cn/2020/07/29/cmake-10-refine-compile-speed-and-program-performance/)


#### English

- [(CMake Series) Part 1 - Introduction](https://blog.xizhibei.me/en/2020/03/09/cmake-1-introduction/)
- [(CMake Series) Part 2 - Third-Party Dependency Management](https://blog.xizhibei.me/en/2020/03/15/cmake-2-third-party-dependances-management/)
- [(CMake Series) Part 3 - ExternalProject Practice](https://blog.xizhibei.me/en/2020/03/23/cmake-3-external-project-practise/)
- [(CMake Series) Part 4 - Testing with GoogleTest](https://blog.xizhibei.me/en/2020/04/05/cmake-4-test-with-google-test/)
- [(CMake Series) Part 5 - Installation, Packaging, and Export](https://blog.xizhibei.me/en/2020/04/20/cmake-5-install-package-and-export/)
- [(CMake Series) Part 6 - Creating Documentation with Doxygen, Sphinx, and Breathe](https://blog.xizhibei.me/en/2020/05/19/cmake-6-docs-with-doxygen-sphinx-breathe/)
- [(CMake Series) Part 7 - Common Variables, Functions, and Modules](https://blog.xizhibei.me/en/2020/06/02/cmake-7-common-var-func-and-modules/)
- [(CMake Series) Part 8 - Cross Compiling](https://blog.xizhibei.me/en/2020/06/15/cmake-8-cross-compiling/)
- [(CMake Series) Part 9 - Practical Tips: How to Download Dependencies](https://blog.xizhibei.me/en/2020/06/30/cmake-9-implement-download-extract-file/)
- [(CMake Series) Part 10 - Enhancing Compilation Speed and Program Performance](https://blog.xizhibei.me/en/2020/07/29/cmake-10-refine-compile-speed-and-program-performance/)


### LICENSE
![知识共享许可协议](https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png "署名-非商业性使用-相同方式共享（BY-NC-SA）")

本博客中所有文章采用 [署名-非商业性使用-相同方式共享（BY-NC-SA）](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh) 进行许可。
